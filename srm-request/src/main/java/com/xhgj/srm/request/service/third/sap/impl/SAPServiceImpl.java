package com.xhgj.srm.request.service.third.sap.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dtflys.forest.annotation.BindingVar;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.request.context.ResponseContext;
import com.xhgj.srm.request.dto.sap.SapInventoryDTO;
import com.xhgj.srm.request.dto.sap.SapInventoryQueryForm;
import com.xhgj.srm.request.factory.MapStructFactory;
import com.xhgj.srm.request.service.third.api.SAPApi;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.InvoicePostingParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.InvoicePostingResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.InvoicePostingResult.RETURN;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_076Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_076Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_077Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_077Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_078Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_078Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_079Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_084Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_084Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_086Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_086Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_087Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_087Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_088Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_088Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.PurchaseApplyForOrderUpdateParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.PurchaseApplyForOrderUpdateResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.PurchaseApplyForOrderUpdateResult.Message;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapDrawApplyParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult.UpdatePurchaseOrderRETURNDTO;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.request.vo.sap.SapInventoryVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import com.xhiot.boot.mvc.lock.BootLockByUserFor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SAPServiceImpl implements SAPService {

  @Resource
  SAPApi sapApi;
  @Resource
  BootConfig bootConfig;
  @Resource
  private ApplicationContext applicationContext;
  @Resource
  DownloadThenUpUtil downloadThenUpUtil;

  /**
   * MM034接口：涉及范围新增预付申请、新增提款申请、新增退款申请、预付款冲销
   * 重复提交时报错“您好，检查到您有一条正在处理中的【提交付款申请】任务，请等待5min后再次尝试”
   */
  private static final String LOCK_GROUP_MM_034 = "LOCK_GROUP_MM_034";

  /**
   * MM_031接口：涉及未开票的采购订单退货、直销库订单入库，这两个操作同一时间只能提交一个（包括交叉操作和自身操作）
   * 重复提交时报错“您好，检查到您有一条正在处理中的【新增SAP物料凭证】任务，请等待5min后再次尝试”；
   */
  private static final String LOCK_GROUP_MM_031 = "LOCK_GROUP_MM_031";

  /**
   * 提交付款申请 + 锁
   */
  @Override
  public AdvanceApplyResult sapAdvanceApplyWithLockGroup(AdvanceApplyParam advanceApplyParam) {
    try {
      // 解决同一类调用注解不生效问题
      SAPServiceImpl proxy = applicationContext.getBean(SAPServiceImpl.class);
      return proxy.sapAdvanceApplyWithLockGroupHandler(advanceApplyParam);
    } catch (Exception e) {
      if (e.getMessage().contains("服务器正在处理您的上一个提交操作")) {
        throw new CheckException(
            "您好，检查到您有一条正在处理中的【提交付款申请】任务，请等待5min后再次尝试");
      }
      throw e;
    }
  }

  /**
   * 提交付款申请 + 锁处理
   */
  @BootLockByUserFor(LOCK_GROUP_MM_034)
  public AdvanceApplyResult sapAdvanceApplyWithLockGroupHandler(AdvanceApplyParam advanceApplyParam) {
    return sapAdvanceApply(advanceApplyParam);
  }

  /**
   * 新增付款申请
   */
  @Override
  public AdvanceApplyResult sapAdvanceApply(AdvanceApplyParam advanceApplyParam) {
    ForestResponse<String> response = sapApi.advancePaymentApplication(advanceApplyParam);

    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回付款申请结果");
    }
    AdvanceApplyResult advanceApplyResult =
        JSON.parseObject(response.getResult(), AdvanceApplyResult.class);
    if(StrUtil.equals(advanceApplyResult.getReturnX().getType(), Constants_Sap.SUCCESS_TYPE)){
      return advanceApplyResult;
    }else {
      throw new CheckException("调用sap接口失败:" + advanceApplyResult.getReturnX().getMsg());
    }
  }

  @Override
  public ReceiptVoucherSynchronizationResult sapMaterialVoucherWithLockGroup(ReceiptVoucherSynchronizationParam form) {
    return this.sapMaterialVoucherWithLockGroup(form, new ReceiptVoucherSynchronizationResult());
  }

  /**
   * SAP物料凭证新增接口 + 锁
   * @param form
   * @return
   */
  @Override
  public ReceiptVoucherSynchronizationResult sapMaterialVoucherWithLockGroup(ReceiptVoucherSynchronizationParam form, ReceiptVoucherSynchronizationResult tempResult) {
    try {
      // 解决同一类调用注解不生效问题
      SAPServiceImpl proxy = applicationContext.getBean(SAPServiceImpl.class);
      return proxy.sapMaterialVoucherWithLockGroupHandler(form, tempResult);
    } catch (Exception e) {
      if (e.getMessage().contains("服务器正在处理您的上一个提交操作")) {
        throw new CheckException(
            "您好，检查到您有一条正在处理中的【新增SAP物料凭证】任务，请等待5min后再次尝试");
      }
      throw e;
    }
  }

  /**
   * SAP物料凭证新增接口 + 锁处理
   * @param form
   * @return
   */
  @BootLockByUserFor(LOCK_GROUP_MM_031)
  public ReceiptVoucherSynchronizationResult sapMaterialVoucherWithLockGroupHandler(ReceiptVoucherSynchronizationParam form, ReceiptVoucherSynchronizationResult tempResult) {
    return sapMaterialVoucherWithError(form, tempResult);
  }


  @Override
  public ReceiptVoucherSynchronizationResult sapMaterialVoucherWithError(ReceiptVoucherSynchronizationParam form, ReceiptVoucherSynchronizationResult tempResult) {
    ForestResponse<String> response = sapApi.materialVoucher(form);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回物料凭证新增s结果");
    }
    ReceiptVoucherSynchronizationResult result = JSON.parseObject(response.getResult(),
        new TypeReference<ReceiptVoucherSynchronizationResult>() {});
    MapStructFactory.INSTANCE.updateReceiptVoucherSynchronizationResult(result, tempResult);
    if (result.isSuccess()) {
      return result;
    } else {
      String msg = JSON.toJSONString(result);
      if (CollUtil.isNotEmpty(result.getReturnMessages())) {
        msg = result.getReturnMessages().get(0).getMessage();
      }
      throw new CheckException("SAP系统响应失败，请联系管理员！" + msg);
    }
  }

  /**
   * 财务凭证修改
   */
  @Override
  public AdvanceApplyResult sapDraw(SapDrawApplyParam form) {
    ForestResponse<String> response = sapApi.paymentApplication(form);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回财务凭证修改申请结果");
    }
    AdvanceApplyResult advanceApplyResult =
        JSON.parseObject(response.getResult(), AdvanceApplyResult.class);
    if(StrUtil.equals(advanceApplyResult.getReturnX().getType(),Constants_Sap.SUCCESS_TYPE)){
      return advanceApplyResult;
    }else {
      throw new CheckException("调用sap接口失败:" + advanceApplyResult.getReturnX().getMsg());
    }
  }

  @Override
  public UpdatePurchaseOrderRETURNDTO sapPurchaseOrderWithAlarm(UpdatePurchaseOrderSapParam form, String orderNo) {
    ForestResponse<String> response = sapApi.purchaseOrder(form);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回采购订单创建/修改结果");
    }
    UpdatePurchaseOrderSapResult updatePurchaseOrderSapResult =
        JSON.parseObject(response.getResult(), UpdatePurchaseOrderSapResult.class);
    if(StrUtil.equals(updatePurchaseOrderSapResult.getReturnSap().getType(),Constants_Sap.SUCCESS_TYPE)){
      return updatePurchaseOrderSapResult.getReturnSap();
    }else {
      if (StrUtil.isNotBlank(orderNo)) {
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【" + env + "环境 " + bootConfig.getAppName() + "】 【" + orderNo + "】调用SAP " + "新增采购订单失败：" + "请求参数：" + JSON.toJSONString(form)
                + "请求结果：" + JSON.toJSONString(updatePurchaseOrderSapResult) + " ，请及时处理！", env);
      }
      throw new CheckException("调用sap接口失败:" + updatePurchaseOrderSapResult.getReturnSap().getMsg());
    }
  }

  @Override
  public SapPayStateResult sapPayState(SapPayStateParam param) {
    ForestResponse<String> response = sapApi.queryPaymentStatus(param);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回付款状态结果");
    }
    SapPayStateResult sapPayStateResult = JSON.parseObject(response.getResult(), SapPayStateResult.class);
    if (CollUtil.isNotEmpty(sapPayStateResult.getReturnX())  && StrUtil.equals(sapPayStateResult.getReturnX().get(0).getType(), Constants_Sap.SUCCESS_TYPE)) {
//      RETURNDTO returndto = sapPayStateResult.getReturnX().get(0);
//      if (returndto.getZzpaystatus().equals("G")) {
//        returndto.setZzbuchetfileurl("https://test-xhgj-xhmall-product.oss-cn-shanghai.aliyuncs.com/srm/sap/202408072367566574.pdf");
//      }
      return sapPayStateResult;
    } else {
      final String[] msg = {""};
      if (CollUtil.isNotEmpty(sapPayStateResult.getReturnX())) {
        sapPayStateResult.getReturnX().forEach(returnMessage -> {
          msg[0] += returnMessage.getMsg();
        });
      }
      throw new CheckException("调用sap接口失败:" + msg[0]);
    }
  }

  @Override
  public MM_075Result sapDirectReturnWithError(MM_075Param param) {
    return sapDirectReturnWithError(param, new MM_075Result());
  }

  @Override
  public MM_075Result sapDirectReturnWithError(MM_075Param param, MM_075Result tempResult) {
    try {
      ForestResponse<String> response = sapApi.directReturn(param);
      if (StrUtil.isBlank(response.getResult())) {
        throw new CheckException("未返回直销库退货结果");
      }
      MM_075Result mm075Result = JSON.parseObject(response.getResult(), new TypeReference<MM_075Result>() {});
      MapStructFactory.INSTANCE.updateMM_075Result(mm075Result, tempResult);
      if (StrUtil.equals(mm075Result.getReturnMessages().get(0).getType(), Constants_Sap.SUCCESS_TYPE)) {
        return mm075Result;
      } else {
        throw new CheckException("调用sap接口失败:" + JSON.toJSONString(mm075Result));
      }
    } catch (Exception e) {
      log.error("调用sap接口失败", e);
      throw new CheckException(
          "SAP网络异常请求失败，请联系管理员处理。请求时间"
              + DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
    }
  }

  @Override
  public List<SapInventoryVO> sapInventoryQuery(SapInventoryQueryForm form) {
    ForestResponse<String> response = sapApi.sapInventoryQuery(form);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回库存查询结果");
    }
    SapInventoryDTO sapInventoryDTO = JSON.parseObject(response.getResult(),
        new TypeReference<SapInventoryDTO>() {});
    if (StrUtil.equals(sapInventoryDTO.getType(), Constants_Sap.SUCCESS_TYPE)) {
      return sapInventoryDTO.getReturnMessages().stream()
          .map(MapStructFactory.INSTANCE::toSapInventoryVO).collect(Collectors.toList());
    } else if (StrUtil.equals(sapInventoryDTO.getType(), Constants_Sap.ERROR_TYPE)
        && sapInventoryDTO.getMsg().contains(Constants_Sap.EMPTY_QUERY_RESULT)) {
      return Collections.emptyList();
    }else {
      throw new CheckException("调用sap接口失败:" + JSON.toJSONString(sapInventoryDTO));
    }
  }

  @Override
  public InvoicePostingResult transferItemsWithAlarm(InvoicePostingParam form, String invoiceNums) {
    ForestResponse<String> response = sapApi.invoicePosting(form);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回发票过账结果");
    }
    InvoicePostingResult result = JSON.parseObject(response.getResult(), new TypeReference<InvoicePostingResult>() {});
    if (result.isSuccess()) {
      return result;
    } else {
      String env = bootConfig.getEnv();
      DingUtils.sendMsgByWarningRobot("【" + env + "环境 " + bootConfig.getAppName() + "】 进项票审核：【"
          + invoiceNums + "】调用SAP" + "发票过账接口报错："
          + JSON.toJSONString(result) + "；请求参数：" + JSON.toJSONString(form)+ "请及时处理！", env);
      RETURN returnResult = result.getResult();
      if (returnResult == null) {
        returnResult = new RETURN();
        returnResult.setMessageText("未知异常");
        result.setResult(returnResult);
      }
      throw new CheckException("SAP系统响应失败:" + returnResult.getMessageText());
    }
  }

  @Override
  public String uploadBankReceipt(String url) {
    try {
      return downloadThenUpUtil.downloadAndUpload(url, "srm/sap");
    } catch (Exception e) {
      log.error("获取 SAP 银行回单，上传 OSS 报错！", e);
      return null;
    }
  }

  @Override
  public MM_076Result sapTransferOrder(MM_076Param param) {
    MM_076Result mm076Result = new MM_076Result();
    try {
      ForestResponse<String> response = sapApi.sapTransferOrder(param);
      if (StrUtil.isBlank(response.getResult())) {
        throw new CheckException("未返回sap调拨凭证过账结果");
      }
       return JSON.parseObject(response.getResult(), new TypeReference<MM_076Result>() {});
    } catch (Exception e) {
      log.error("调用sap调拨凭证过账接口失败", e);
      mm076Result.setMessage("调用sap调拨凭证过账接口失败："+StrUtil.maxLength(ExceptionUtil.getSimpleMessage(e),500));
      mm076Result.setType(Constants_Sap.ERROR_TYPE);
      return mm076Result;
    }
  }

  @Override
  public MM_077Result sapAsmDisOrderUpdate(MM_077Param param) {
    try {
      ForestResponse<String> response = sapApi.sapAsmDisOrderUpdate(param);
      if (StrUtil.isBlank(response.getResult())) {
        throw new CheckException("未返回sap组装拆卸单创建修改结果");
      }
      MM_077Result mm077Result =
          JSON.parseObject(response.getResult(), new TypeReference<MM_077Result>() {});

      if (StrUtil.equals(mm077Result.getType(), Constants_Sap.SUCCESS_TYPE)) {
        return mm077Result;
      }else {
        throw new CheckException("调用sap接口失败:" + mm077Result.getMsg());
      }
    } catch (Exception e) {
      log.error("调用sap组装拆卸单创建修改接口失败:{}", e.getMessage(), e);
      throw new CheckException(StrUtil.format("调用sap组装拆卸单创建修改接口失败:{}", e.getMessage()));
    }
  }

  @Override
  public MM_078Result sapAsmDisOrderChangeState(MM_078Param param) {
    try {
      ForestResponse<String> response = sapApi.sapAsmDisOrderChangeState(param);
      if (StrUtil.isBlank(response.getResult())) {
        throw new CheckException("未返回sap组装拆卸单状态变更同步结果");
      }
      MM_078Result mm078Result =
          JSON.parseObject(response.getResult(), new TypeReference<MM_078Result>() {});

      if (StrUtil.equals(mm078Result.getType(), Constants_Sap.SUCCESS_TYPE)) {
        return mm078Result;
      }else {
        throw new CheckException("调用sap接口失败:" + mm078Result.getMsg());
      }
    } catch (Exception e) {
      log.error("调用sap组装拆卸单状态变更同步接口失败:{}", e.getMessage(), e);
      throw new CheckException(StrUtil.format("调用sap组装拆卸单状态变更同步接口失败:{}", e.getMessage()));
    }
  }

  @Override
  public MM_079Result sapAsmDisOrderInfoPosting(MM_079Param param) {
    try {
      ForestResponse<String> response = sapApi.sapAsmDisOrderInfoPosting(param);
      if (StrUtil.isBlank(response.getResult())) {
        throw new CheckException("未返回sap组装拆卸信息过账接口结果");
      }
      MM_079Result mm079Result =
          JSON.parseObject(response.getResult(), new TypeReference<MM_079Result>() {});
      if (mm079Result == null || CollUtil.isEmpty(mm079Result.getItem()) || !StrUtil.equals(
          mm079Result.getItem().get(0).getType(), Constants_Sap.SUCCESS_TYPE)) {
        throw new CheckException("调用sap组装拆卸信息过账接口失败:" + JSON.toJSONString(mm079Result));
      }
        return mm079Result;
    } catch (Exception e) {
      log.error("调用sap组装拆卸信息过账接口失败:{}", e.getMessage(), e);
      throw new CheckException(StrUtil.format("调用sap组装拆卸信息过账接口失败:{}", e.getMessage()));
    }
  }

  @Override
  public boolean sapUpdatePurchaseForOrder(List<PurchaseApplyForOrderUpdateParam> param) {
    Assert.notNull(param);
    PurchaseApplyForOrderUpdateResult result;
    try {
      String responseBody = sapApi.sapPurchaseApplyForOrderUpdate(param).getResult();
      ResponseContext.setResponse(responseBody);
      if (StrUtil.isBlank(responseBody)) {
        throw new CheckException("未返回采购申请单修改结果");
      }
      result =
          JSON.parseObject(responseBody, new TypeReference<PurchaseApplyForOrderUpdateResult>() {});
    } catch (CheckException e) {
      throw e;
    } catch (Exception e) {
      log.error("调用sap接口失败参数：", param);
      log.error("调用sap接口失败", e);
      sendDingTalkMessageForAddPayable(e.getMessage());
      throw new CheckException("调用sap接口失败", e);
    }
    return result.isSuccess();
  }

  /**
   * 发送生成erp应付单失败钉钉消息
   */
  private void sendDingTalkMessageForAddPayable(String message) {
    String env = bootConfig.getEnv();
    DingUtils.sendMsgByWarningRobot(
        "【" + env + "环境 " + bootConfig.getAppName() + "】 更新采购申请单调用SAP报错" + message + "请及时处理！", env);
  }

  /**
   * 采购订单状态更新
   */
  @Override
  public MM_084Result sapPurchaseOrderStatusUpdate(MM_084Param param) {
    ForestResponse<String> response = sapApi.sapPurchaseOrderStatusUpdate(param);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("未返回采购订单状态更新结果");
    }
    MM_084Result result =
        JSON.parseObject(response.getResult(), new TypeReference<MM_084Result>() {});
    if (StrUtil.equals(result.getStatus(), Constants_Sap.SUCCESS_TYPE)) {
      return result;
    } else {
      throw new CheckException("采购订单状态更新失败:" + result.getMsg());
    }
  }

  @Override
  public MM_086Result sapOrderInquiries(MM_086Param param) {
    ForestResponse<String> response = sapApi.sapOrderInquiries(param);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("SAP_MM086订单查询未返回结果");
    }
    MM_086Result result =
        JSON.parseObject(response.getResult(), new TypeReference<MM_086Result>() {});
    if (StrUtil.equals(result.getType(), Constants_Sap.SUCCESS_TYPE)) {
      return result;
    } else {
      throw new CheckException("SAP_MM086订单查询返回结果失败:" + result.getMsg());
    }
  }

  @Override
  public MM_087Result sapMaterialCardInquiries(MM_087Param param) {
    ForestResponse<String> response = sapApi.sapMaterialCardInquiries(param);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("SAP_MM087资料卡片查询未返回结果");
    }
    MM_087Result result =
        JSON.parseObject(response.getResult(), new TypeReference<MM_087Result>() {});
    if (StrUtil.equals(result.getType(), Constants_Sap.SUCCESS_TYPE)) {
      return result;
    } else {
      throw new CheckException("SAP_MM087资料卡片查询返回结果失败:" + result.getMsg());
    }
  }

  @Override
  public MM_088Result sapBomInventoryInquiries(MM_088Param param) {
    ForestResponse<String> response = sapApi.sapBomInventoryInquiries(param);
    if (StrUtil.isBlank(response.getResult())) {
      throw new CheckException("SAP_MM088BOM清单查询未返回结果");
    }
    MM_088Result result =
        JSON.parseObject(response.getResult(), new TypeReference<MM_088Result>() {});
    if (StrUtil.equals(result.getType(), Constants_Sap.SUCCESS_TYPE)) {
      return result;
    } else {
      throw new CheckException("SAP_MM088BOM清单查询返回结果失败:" + result.getMsg());
    }
  }

  @Override
  public ReceiptOrReturnReversalResult sapReceiptOrReturnReversal(ReceiptOrReturnReversalParams param) {
    ForestResponse<String> stringForestResponse = sapApi.sapMaterialVoucherReverse(param);
    if (StrUtil.isBlank(stringForestResponse.getResult())) {
      throw new CheckException("SAP未返回入库单/退库单冲销结果");
    }
    ReceiptOrReturnReversalResult result =
        JSON.parseObject(stringForestResponse.getResult(), new TypeReference<ReceiptOrReturnReversalResult>() {});
    if (StrUtil.equals(result.getReturnX().getType(), Constants_Sap.SUCCESS_TYPE)) {
      return result;
    } else {
      String msg = result.getReturnX().getMsg();
      throw new CheckException("SAP入库单/退库单冲销失败:" + msg);
    }
  }
}
