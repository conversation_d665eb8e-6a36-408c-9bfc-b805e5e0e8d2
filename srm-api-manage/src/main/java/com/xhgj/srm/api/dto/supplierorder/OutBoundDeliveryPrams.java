package com.xhgj.srm.api.dto.supplierorder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.map.TypeAwareMap;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/5/26
 * @desciption:
 */
@ApiModel("出库单列表查询入参")
@Data
public class OutBoundDeliveryPrams implements Serializable, BaseDefaultSearchSchemeForm {
    @ApiModelProperty("勾选的数据行")
    private List<String> ids;
    @ApiModelProperty("搜索方案 id")
    private String schemeId;
    @ApiModelProperty(value = "采购订单号")
    private String orderCode;
    @ApiModelProperty(value = "SAP物料凭证号")
    private String sapProductVoucherNo;
    @ApiModelProperty(value = "SAP退库单采购订单号")
    private String returnOrderCode;
    @ApiModelProperty(value = "批号")
    private String batchNo;
    @ApiModelProperty(value = "已开红票数量")
    private Integer invoiceQuantity;
    @ApiModelProperty(value = "已开红票数量操作符")
    private LogicalOperatorsEnums invoiceQuantityOperator;
    @ApiModelProperty(value = "是否需要开红票 0否 1是")
    private Integer isNeedRedInvoice;
    @ApiModelProperty(value = "退库时间起")
    private String returnTimeStart;
    @ApiModelProperty(value = "退库时间止")
    private String returnTimeEnd;
    @ApiModelProperty(value = "退库原因")
    private String returnReason;
    @ApiModelProperty(value = "退库仓库")
    private String returnWarehouse;
    @ApiModelProperty(value = "仓库执行状态 0否 1是")
    private String warehouseExecutionStatus;
    @ApiModelProperty(value = "快递单号")
    private String expressNo;
    @ApiModelProperty(value = "物流公司")
    private String expressCompany;
    @ApiModelProperty(value = "物料编码")
    private String productCode;
    @ApiModelProperty(value = "品牌")
    private String brand;
    @ApiModelProperty(value = "物料名称")
    private String productName;
    @ApiModelProperty(value = "规格型号")
    private String specification;
    @ApiModelProperty(value = "退库数量")
    private BigDecimal returnQuantity;
    @ApiModelProperty(value = "单退库数量操作符")
    private LogicalOperatorsEnums returnQuantityOperator;
    @ApiModelProperty(value = "关联发票号")
    private String invoiceNo;
    @ApiModelProperty(value = "冲销状态")
    private String writeOffState;
    @ApiModelProperty("供应商名称")
    private String supplierName;
    @ApiModelProperty("采购员")
    private String purchaseMan;
    @ApiModelProperty("采购部门")
    private String purchaseDept;

    private Integer pageNo;

    private Integer pageSize;

   private String userId;
  /**
   * 用户组
   */
  private String userGroup;

    public Integer getPageNo() {
      if (pageNo == null) {
          return 1;
      }
      return pageNo;
    }

    public Integer getPageSize() {
      if (pageSize == null) {
          return 10;
      }
      return pageSize;
    }

    public Map<String,Object> toQueryMap(List<String> userNameList, String purchaseId,
        String createMan) {
      Map<String, Object> map = new TypeAwareMap<>();
      map.put("ids", ids);
      map.put("userNameList", userNameList);
      map.put("purchaseId", purchaseId);
      map.put("createMan", createMan);
      map.put("orderCode", orderCode);
      map.put("sapProductVoucherNo", sapProductVoucherNo);
      map.put("returnOrderCode", returnOrderCode);
      map.put("batchNo", batchNo);
      map.put("invoiceQuantity", invoiceQuantity);
      map.put("invoiceQuantityOperator", invoiceQuantityOperator);
      map.put("isNeedRedInvoice", isNeedRedInvoice);
      if (StrUtil.isNotBlank(returnTimeStart)) {
        Long startTime = DateUtil.beginOfDay(DateUtil.parse(returnTimeStart, DatePattern.NORM_DATE_PATTERN)).getTime();
        map.put("startTime", startTime);
      }
      if (StrUtil.isNotBlank(returnTimeEnd)) {
        Long endTime = DateUtil.endOfDay(DateUtil.parse(returnTimeEnd, DatePattern.NORM_DATE_PATTERN)).getTime();
        map.put("endTime", endTime);
      }
      map.put("returnReason", returnReason);
      map.put("returnWarehouse", returnWarehouse);
      map.put("warehouseExecutionStatus", warehouseExecutionStatus);
      map.put("expressNo", expressNo);
      map.put("expressCompany", expressCompany);
      map.put("productCode", productCode);
      map.put("brand", brand);
      map.put("productName", productName);
      map.put("specification", specification);
      map.put("returnQuantity", returnQuantity);
      map.put("returnQuantityOperator", returnQuantityOperator);
      map.put("invoiceNo", invoiceNo);
      map.put("writeOffState", writeOffState);
      map.put("supplierName", supplierName);
      map.put("purchaseMan", purchaseMan);
      map.put("purchaseDept", purchaseDept);
      map.put("userId", userId);
      map.put("pageNo", getPageNo());
      map.put("pageSize", getPageSize());
      map.put("userGroup", userGroup);
      return map;
    }
}
