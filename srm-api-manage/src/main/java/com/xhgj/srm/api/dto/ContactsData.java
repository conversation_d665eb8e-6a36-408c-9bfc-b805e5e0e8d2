package com.xhgj.srm.api.dto;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.Data;

@Data
public class ContactsData {

    private String id;
    private String name;
    private String sex;
    private String phone;
    private String mobile;
    private String mail;
    private String duty;
    private String area;
    private String createMan;
    private String createManId;
    private String isEdit;
    private String updateTime;
    private String fax;
    private String isDefault;

    public ContactsData(Contact contact) {
        this.id = contact.getId();
        this.name = !StringUtils.isNullOrEmpty(contact.getName())?contact.getName():"";
        this.sex = !StringUtils.isNullOrEmpty(contact.getSex())?contact.getSex():"";
        this.phone = !StringUtils.isNullOrEmpty(contact.getPhone())?contact.getPhone():"";
        this.mail = !StringUtils.isNullOrEmpty(contact.getMail())?contact.getMail():"";
        this.duty = !StringUtils.isNullOrEmpty(contact.getDuty())?contact.getDuty():"";
        this.area = !StringUtils.isNullOrEmpty(contact.getArea())?contact.getArea():"";
        this.createManId = !StringUtils.isNullOrEmpty(contact.getCreateMan())?contact.getCreateMan():"";
        this.updateTime = DateUtils.formatTimeStampToNormalDateTime(contact.getCreateTime());
        this.fax = !StringUtils.isNullOrEmpty(contact.getFax())?contact.getFax():"";
        this.isDefault = StrUtil.emptyToDefault(contact.getIsDefault(),"0");
    }
}
