package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/17 13:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeRecordTableDTO {
  @ApiModelProperty("字段编码")
  private String fieldCode;

  @ApiModelProperty("字段名")
  private String fieldName;

  @ApiModelProperty("旧值")
  private String oldValue;

  @ApiModelProperty("新值")
  private String newValue;

  @ApiModelProperty("操作人名称")
  private String operateManName;

  @ApiModelProperty("操作时间")
  private Long operateTime;
}
