package com.xhgj.srm.api.dto.supplierOrder;

import com.xhgj.srm.api.dto.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/** <AUTHOR> @ClassName QuerySupplierOrderDTO */
@Data
public class SupplierOrderQueryDTO extends BaseQuery {
  @ApiModelProperty("采购订单号")
  private String supplierOrderCode;

  @ApiModelProperty("订单状态(0--待履约,1--履约中,2--完成,''--全部)")
  private String supplierOrderState;

  @ApiModelProperty("是否有待确认")
  private Boolean confirmState;

  @ApiModelProperty("是否有取消")
  private Boolean cancelState;

   @ApiModelProperty("是否有退货")
  private Boolean returnState;

  @ApiModelProperty("是否厂直发")
  private Boolean supplierDirectShipment;

  @ApiModelProperty("采购组织")
  private String supplierOrderGroupName;

  @ApiModelProperty("收件人")
  private String supplierOrderReceiveMan;

  @ApiModelProperty("开票状态")
  private String supplierOpenInvoiceState;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty("开始时间")
  private String supplierOrderStartTime;

  private Long supplierOrderStartTimeLong;

  @ApiModelProperty("是否有拒单")
  private Boolean refuseState;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty("结束时间")
  private String supplierOrderEndTime;
  private Long supplierOrderEndTimeLong;

}
