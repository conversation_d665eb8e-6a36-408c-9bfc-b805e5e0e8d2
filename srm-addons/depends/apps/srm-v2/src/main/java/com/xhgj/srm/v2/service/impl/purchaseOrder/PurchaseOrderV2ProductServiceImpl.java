package com.xhgj.srm.v2.service.impl.purchaseOrder;/**
 * @since 2025/4/28 9:40
 */

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.purchase.order.PurchaseOrderProductFilterTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.permission.OperatorPermission;
import com.xhgj.srm.jpa.dto.permission.SearchPermission;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderProductStatistics;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.jpa.util.BatchQueryUtil;
import com.xhgj.srm.jpa.util.LazyLoaderContext;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO.WWDTO;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.v2.dao.SupplierOrderDetailV2Dao;
import com.xhgj.srm.v2.dto.UpdateProductDetailParamV2;
import com.xhgj.srm.v2.dto.purchaseOrder.SupplierOrderCountV2DTO;
import com.xhgj.srm.v2.form.purchaseOrder.product.PurchaseOrderProductExport;
import com.xhgj.srm.v2.form.purchaseOrder.product.PurchaseOrderProductTableHeaderV2Query;
import com.xhgj.srm.v2.form.purchaseOrder.product.PurchaseOrderProductV2QueryForm;
import com.xhgj.srm.v2.provider.ExportFiledTemplateEventProvider;
import com.xhgj.srm.v2.provider.PermissionTypeProvider;
import com.xhgj.srm.v2.repository.PurchaseApplyForOrderV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderProductV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2ProductService;
import com.xhgj.srm.v2.vo.PurchaseOrderProductV2DetailedVO;
import com.xhgj.srm.v2.vo.purchaseOrder.PurchaseOrderProductV2ListVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.upload.config.UploadConfig;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PurchaseOrderV2ProductServiceImpl implements PurchaseOrderV2ProductService {

  @Resource
  private PermissionTypeProvider permissionTypeProvider;
  @Resource
  private SupplierOrderDetailV2Dao supplierOrderDetailV2Dao;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;
  @Resource
  private SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  private PurchaseApplyForOrderV2Repository purchaseApplyForOrderV2Repository;
  @Resource
  private SupplierOrderProductV2Repository supplierOrderProductV2Repository;
  @Resource
  private FileRepository fileRepository;
  @Resource
  private UploadConfig uploadConfig;
  @Resource
  private PaymentApplyDetailRepository paymentApplyDetailRepository;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Resource
  private SAPService sapService;
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private ExportFiledTemplateEventProvider exportFiledTemplateEventProvider;
  @Resource
  private BatchTaskMqSender batchTaskMqSender;
  @Resource
  private MissionUtil missionUtil;
  @Resource
  private MissionRepository missionRepository;


  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  @Override
  public PageResult<PurchaseOrderProductV2ListVO> getPagePurchaseOrderProductPageRef(
      PurchaseOrderProductV2QueryForm form, User user) {
    String currUser = PurchaseOrderV2BaseServiceImpl.getCurrUser(user);
    SearchPermission searchPermission = permissionTypeProvider.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission = permissionTypeProvider.mergePermission(searchPermission, new OperatorPermission());
    Page<SupplierOrderDetailV2> page =
        LazyLoaderContext.lazyLoad(() -> supplierOrderDetailV2Dao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission)));
    List<SupplierOrderDetailV2> content = page.getContent();
    if (CollUtil.isEmpty(content)) {
      return PageResult.empty(form.getPageNo(), form.getPageSize());
    }
    // 1.获取orderToFormId
    List<String> orderToFormIds =
        content.stream().map(SupplierOrderDetailV2::getOrderToFormId).distinct()
            .collect(Collectors.toList());
    orderToFormIds.add("-1");
    // 1.1 批量获取SupplierOrderToForm
    Map<String, SupplierOrderToFormV2> id2SupplierOrderToForm =
        BatchQueryUtil.batchInQuery(orderToFormIds,
                batchIds -> supplierOrderToFormV2Repository.findAllById(batchIds)).stream()
            .collect(Collectors.toMap(SupplierOrderToFormV2::getId, Function.identity()));
    // 2.获取supplierOrderId
    List<String> supplierOrderIds =
        id2SupplierOrderToForm.values().stream().map(SupplierOrderToFormV2::getSupplierOrderId).distinct()
            .collect(Collectors.toList());
    supplierOrderIds.add("-1");
    Map<String, SupplierOrderV2> id2SupplierOrder =
        BatchQueryUtil.batchInQuery(supplierOrderIds,
                batchIds -> supplierOrderV2Repository.findAllById(batchIds)).stream()
            .collect(Collectors.toMap(SupplierOrderV2::getId, Function.identity()));
    // 3.采购申请
    List<String> applyIds = content.stream().map(SupplierOrderDetailV2::getPurchaseApplyForOrderId)
        .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
    Map<String, PurchaseApplyForOrderV2> id2PurchaseApplyForOrder =
        BatchQueryUtil.batchInQuery(applyIds,
                batchIds -> purchaseApplyForOrderV2Repository.findAllById(batchIds)).stream()
            .collect(Collectors.toMap(PurchaseApplyForOrderV2::getId, Function.identity()));
    // 4.是否存在冲销
    // 批量查询是否有冲销
    List<String> hasReversal = BatchQueryUtil.batchInQuery(supplierOrderIds,
        batchIds -> supplierOrderToFormV2Repository.findSupplierIdsByStatus(
            ListUtil.toList(SupplierOrderFormType.RETURN.getType(), SupplierOrderFormType.WAREHOUSING.getType()),
            ListUtil.toList(SupplierOrderFormStatus.REVERSAL.getStatus(), SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus()),
            batchIds));
    // 5.supplierOrderProduct --- supplierOrderDetail带有supplierOrderProduct
    List<String> productIds = content.stream().map(SupplierOrderDetailV2::getOrderProductId)
        .distinct().collect(Collectors.toList());
    productIds.add("-1");
    Map<String, SupplierOrderProductV2> id2SupplierProduct =
        BatchQueryUtil.batchInQuery(productIds,
                batchIds -> supplierOrderProductV2Repository.findAllById(batchIds)).stream()
            .collect(Collectors.toMap(SupplierOrderProductV2::getId, Function.identity(), (v1, v2) -> v1));
    // 6.批量查询是否待签收 -- 存在的返回采购订单id
    List<String> hasWaitReceipt = BatchQueryUtil.batchInQuery(supplierOrderIds,
        batchIds -> supplierOrderToFormV2Repository.findSupplierIdsByStatus(
            Collections.singletonList(SupplierOrderFormType.DELIVER.getType()),
            Collections.singletonList(SupplierOrderFormStatus.WAIT_RECEIPT.getStatus()),
            batchIds));
    // 7.批量获取采购订单合同附件
    List<File> orderContractFiles = BatchQueryUtil.batchInQuery(supplierOrderIds,
        batchIds -> fileRepository.findByRelationIdInAndRelationTypeAndState(
            batchIds, Constants_FileRelationType.ORDER_CONTRACT, Constants.STATE_OK));
    Map<String, List<File>> fileMap = orderContractFiles.stream()
        .collect(Collectors.groupingBy(File::getRelationId));
    // 8.批量获取有预付申请的订单号
    // todo 需要优化从财务凭证取
    List<String> supplierOrderCodes =
        id2SupplierOrder.values().stream().map(SupplierOrderV2::getCode).distinct()
            .collect(Collectors.toList());
    supplierOrderCodes.add("-1");
    List<String> prePayOrderCodes = BatchQueryUtil.batchInQuery(supplierOrderCodes,
        batchCodes -> paymentApplyDetailRepository.getSupplierOrderNoByApplyType(
            PaymentApplyTypeEnums.ADVANCE.getKey(), batchCodes));
    // 9.库位信息
    List<String> groupCodes =
        id2SupplierOrder.values().stream().map(SupplierOrderV2::getGroupCode).distinct().collect(Collectors.toList());
    groupCodes.add("-1");
    List<InventoryLocation> inventoryLocations = BatchQueryUtil.batchInQuery(groupCodes,
        batchCodes -> inventoryLocationRepository.findAllByGroupCodeInAndState(
            batchCodes, Constants.STATE_OK));
    Map<String, List<InventoryLocation>> groupCode2InventoryLocation =
        inventoryLocations.stream().collect(Collectors.groupingBy(InventoryLocation::getGroupCode));
    // 拼接数据
    List<PurchaseOrderProductV2ListVO> vos = page.getContent().stream().map(
        item -> {
          SupplierOrderProductV2 supplierOrderProduct = id2SupplierProduct.get(item.getOrderProductId());
          if (supplierOrderProduct == null) {
            throw new CheckException("当前订单明细" + item.getId() + "的物料信息查询不到！");
          }
          SupplierOrderToFormV2 supplierOrderToForm = id2SupplierOrderToForm.get(item.getOrderToFormId());
          if (supplierOrderToForm == null) {
            throw new CheckException("当前订单明细" + item.getId() + "的form表单查询不到！");
          }
          SupplierOrderV2 supplierOrder = id2SupplierOrder.get(supplierOrderToForm.getSupplierOrderId());
          if (supplierOrder == null) {
            throw new CheckException("当前订单明细" + item.getId() + "的采购订单查询不到！");
          }
          PurchaseOrderProductV2ListVO vo = new PurchaseOrderProductV2ListVO(supplierOrder, supplierOrderProduct, item);
          vo.setSalesman(supplierOrderProduct.getSalesman());
          vo.setFollowUpPersonName(supplierOrderProduct.getFollowUpPersonName());
          vo.setBusinessCompanyName(supplierOrderProduct.getBusinessCompanyName());
          vo.setMakeManName(supplierOrderProduct.getMakeManName());
          vo.setSoldToParty(supplierOrderProduct.getSoldToParty());
          //是否上传合同
          List<File> files = fileMap.getOrDefault(supplierOrder.getId(), new ArrayList<>());
          vo.setUploadContract(CollUtil.isNotEmpty(files));
          //合同文件
          vo.makeContractFiles(files, uploadConfig.getUploadPath());
          // 设置是否有冲销
          if (hasReversal.contains(supplierOrder.getId())) {
            vo.setWriteOffState(true);
          } else {
            vo.setWriteOffState(false);
          }
          // 设置是否有待签收
          if (hasWaitReceipt.contains(item.getId())) {
            vo.setUnReceipt(true);
          } else {
            vo.setUnReceipt(false);
          }
          // 设置deleteStorage
          if (SupplierOrderState.STAGING.getOrderState().equals(supplierOrder.getOrderState())
              || SupplierOrderState.REJECT.getOrderState().equals(supplierOrder.getOrderState())) {
            vo.setDeleteStorage(("admin".equals(user.getName())|| currUser.equals(supplierOrder.getPurchaseMan())
            ));
          }else{
            vo.setDeleteStorage(false);
          }
          // 采购申请
          if (StrUtil.isNotEmpty(item.getPurchaseApplyForOrderId())) {
            PurchaseApplyForOrderV2 purchaseApplyForOrder = id2PurchaseApplyForOrder.get(item.getPurchaseApplyForOrderId());
            if (purchaseApplyForOrder != null) {
              //采购申请单号
              vo.setPurchaseApplyCode(purchaseApplyForOrder.getApplyForOrderNo());
              vo.setApplyForType(purchaseApplyForOrder.getApplyForType());
              vo.setIsWorryOrder(purchaseApplyForOrder.getIsWorryOrder());
            }
          }
          // 是否预付款
          if (CollUtil.isNotEmpty(prePayOrderCodes)) {
            if (prePayOrderCodes.contains(supplierOrder.getCode())) {
              vo.setPrePay(true);
            } else {
              vo.setPrePay(false);
            }
          } else {
            vo.setPrePay(false);
          }
          // stockAddr - 对应warehouse
          List<InventoryLocation> findElementInventoryLocations =
              groupCode2InventoryLocation.getOrDefault(supplierOrder.getGroupCode(),
                  new ArrayList<>());
          InventoryLocation findOne = findElementInventoryLocations
              .stream().filter(item1 -> item1.getWarehouse().equals(item.getWarehouse()))
              .findFirst().orElse(null);
          vo.setWarehouse(StrUtil.EMPTY);
          if (findOne != null) {
            vo.setWarehouse(findOne.getWarehouseName());
          }

          return vo;
        }
    ).collect(Collectors.toList());
    return PageResultBuilder.buildPageResult(page, vos);
  }

  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  @Override
  public SupplierOrderCountV2DTO getOrderProductCount(PurchaseOrderProductV2QueryForm form,
      User user) {
    String createMan = user.getId();
    SearchPermission searchPermission = permissionTypeProvider.getSearchPermission(user, form.getUserGroup(),
        Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission = permissionTypeProvider.mergePermission(searchPermission, new OperatorPermission());
    form.setPageNo(1);
    form.setPageSize(1);
    form.setOrderState(SupplierOrderState.WAIT);
    long waitCount =
        supplierOrderDetailV2Dao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    form.setOrderState(SupplierOrderState.IN_PROGRESS);
    long progressCount =
        supplierOrderDetailV2Dao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    form.setOrderState(SupplierOrderState.NOT_REVIEWED);
    long notReviewed =
        supplierOrderDetailV2Dao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    SupplierOrderCountV2DTO supplierOrderCountDTO = new SupplierOrderCountV2DTO();
    supplierOrderCountDTO.setWaitCount(waitCount);
    supplierOrderCountDTO.setInProgressCount(progressCount);
    supplierOrderCountDTO.setNotReviewedCount(notReviewed);
    return supplierOrderCountDTO;
  }

  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  @Override
  public PurchaseOrderProductStatistics getPagePurchaseOrderStatisticsForProduct(PurchaseOrderProductV2QueryForm form, User user) {
    SearchPermission searchPermission =
        permissionTypeProvider.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        permissionTypeProvider.mergePermission(searchPermission, new OperatorPermission());
    return supplierOrderDetailV2Dao.getPagePurchaseOrderStatistics2(form.toQueryMap(mergeUserPermission));
  }

  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  @Override
  public List<Object> getProductListByTableHeaderRef(PurchaseOrderProductTableHeaderV2Query query, User user) {
    SearchPermission searchPermission =
        permissionTypeProvider.getSearchPermission(user, query.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false, query.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        permissionTypeProvider.mergePermission(searchPermission, new OperatorPermission());
    List<Object> tableHeader = supplierOrderDetailV2Dao.getProductListByTableHeaderRef(query.toQueryMap(mergeUserPermission));
    //数据转化
    List<Object> resultList =
        tableHeader.stream().filter(o -> {
          if (o instanceof String) {
            return StrUtil.isNotBlank((String) o);
          }
          return o != null;
        }).collect(Collectors.toList());
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderProductFilterTypeEnum.PRE_PAY.getKey())
        || StrUtil.equals(query.getFilterType(),
        PurchaseOrderProductFilterTypeEnum.UPLOAD_CONTRACT.getKey()) || StrUtil.equals(
        query.getFilterType(), PurchaseOrderProductFilterTypeEnum.SCP.getKey())) {
      return resultList.stream().map(
          s -> StrUtil.equals(Constants.YES, (String)s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderProductFilterTypeEnum.LOSS.getKey())) {
      return resultList.stream().map(
          s -> BooleanUtil.isTrue((Boolean) s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderProductFilterTypeEnum.IS_WORRY_ORDER.getKey())) {
      return resultList.stream().filter(Objects::nonNull).map(
          s -> StrUtil.equals(Constants.IS_WORRY_ORDER,(String)s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    PurchaseOrderProductFilterTypeEnum filterType = PurchaseOrderProductFilterTypeEnum.getByType(query.getFilterType());
    if (filterType == null) {
      return resultList;
    }
    switch (filterType) {
      case ORDER_STATUS:
        // 转换为列表一致的数据
        return resultList.stream().map(
                s -> SupplierOrderState.findValueByOrderStateWithoutError(Convert.toStr(s)))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      case SUPPLIER_INVOICE:
        return resultList.stream()
            .map(s -> Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE_NEW.get(Convert.toStr(s)))
            .filter(Objects::nonNull).collect(Collectors.toList());
      case ORDER_TYPE:
      case PURCHASE_APPLY_TYPE:
      default:
        return resultList;
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateProductDetail(UpdateProductDetailParamV2 param) {
    Assert.notNull(param);
    SupplierOrderV2 supplierOrder =supplierOrderV2Repository.findById(param.getId())
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderV2.class, param.getId()));
    if (StrUtil.equals(supplierOrder.getOrderState(),
        SupplierOrderState.UNAUDITED.getOrderState())) {
      throw new CheckException("审核中的订单不能修改明细，请耐心等待审核通过");
    }
    SupplierOrderToFormV2 supplierOrderToForm =
        Optional.ofNullable(supplierOrderToFormV2Repository.getFirstByTypeAndSupplierOrderIdAndState(
                SupplierOrderFormType.DETAILED.getType(), supplierOrder.getId(), Constants.STATE_OK))
            .orElseThrow(() -> new CheckException("【" + supplierOrder.getId() + "】未找到该订单的订单明细，请联系管理员"));

    List<ITEMDTO> items = new ArrayList<>();
    //订货金额差值 --------- 经排查allDifferencePrice逻辑已被去除
    BigDecimal allDifferencePrice = BigDecimal.ZERO;
    // 物料信息
    List<UpdateProductDetailParamV2.ProductDetail> productList = param.getProductList();
    for (UpdateProductDetailParamV2.ProductDetail productDetail : productList) {
      SupplierOrderDetailV2 detail =
          supplierOrderDetailV2Repository.findById(productDetail.getId()).orElse(null);
      if (detail==null) {
        continue;
      }
      if (NumberUtil.isGreater(detail.getStockInputQty(), productDetail.getNum())) {
        throw new CheckException("修改后的订货数量不能小于入库数量");
      }
      if (NumberUtil.isGreater(productDetail.getNum(), detail.getNum())) {
        throw new CheckException("您不能调大订货数量，如需调大请重新做单");
      }
      // 剩余入库数量 = 订货数量 - 取消数量 - 已入库数量
      BigDecimal num1 =
          Optional.ofNullable(detail.getNum()).orElse(BigDecimal.ZERO);
      BigDecimal cancel1 =
          Optional.ofNullable(detail.getCancelQty()).orElse(BigDecimal.ZERO);
      BigDecimal stockInputQty1 =
          Optional.ofNullable(detail.getStockInputQty()).orElse(BigDecimal.ZERO);
      BigDecimal remainQty =
          BigDecimalUtil.setScaleBigDecimalHalfUp(num1.subtract(cancel1).subtract(stockInputQty1),
              3);
      if (NumberUtil.equals(remainQty, BigDecimal.ZERO)) {
        throw new CheckException("交货已完成的物料行不允许修改明细");
      }
      //订货数量调整后，如果此行有关联采购申请，需要把采购申请的状态变为可订货，把已订货数量减去本次修改调小的订货数量
      if (NumberUtil.isGreater(NumberUtil.sub(detail.getNum(), productDetail.getNum()),
          BigDecimal.ZERO) && StrUtil.isNotEmpty(detail.getPurchaseApplyForOrderId())) {
        PurchaseApplyForOrderV2 purchaseApplyForOrder =
            purchaseApplyForOrderV2Repository.findById(detail.getPurchaseApplyForOrderId())
                .orElse((null));
        if (purchaseApplyForOrder != null) {
          //已订货数量减去本次修改调小的订货数量
          purchaseApplyForOrder.setOrderGoodsNumber(
              NumberUtil.sub(purchaseApplyForOrder.getOrderGoodsNumber(),
                  NumberUtil.sub(detail.getNum(), productDetail.getNum())));
          purchaseApplyForOrder.updateOrderGoodsStateCheckLock();
          purchaseApplyForOrderV2Repository.save(purchaseApplyForOrder);
        }
      }
      //待发数量=订货数量-已发数量
      detail.setWaitQty(NumberUtil.sub(productDetail.getNum(),detail.getShipQty()));
      //剩余入库数量
      detail.setRemainQty(NumberUtil.sub(productDetail.getNum(), detail.getCancelQty(),
          detail.getStockInputQty()));
      //价税合计=物料单价*数量
      detail.setTotalAmountIncludingTax(NumberUtil.mul(detail.getPrice(),productDetail.getNum()));
      //结算总价=结算单价*数量
      detail.setTotalSettlementPrice(BigDecimalUtil.setScaleBigDecimalHalfUp(
          NumberUtil.mul(productDetail.getNum(), detail.getSettlementPrice()), 2));
      //去掉约定交货日期
      //        detail.setDeliverTime(productDetail.getSupplierDeliverTime());
      detail.setPurchaseDeliverTime(productDetail.getPurchaseDeliverTime());
      detail.setMark(productDetail.getMark());
      // v6.3.0版本线上问题，未设置更新的数据
      detail.setNum(BigDecimalUtil.setScaleBigDecimalHalfUp(productDetail.getNum(), 3));
      detail.setFreightSupplierId(productDetail.getFreightSupplierId());
      detail.setTariffSupplierId(productDetail.getTariffSupplierId());
      detail.setIncidentalSupplierId(productDetail.getIncidentalSupplierId());
      ITEMDTO itemdto = new ITEMDTO();
      itemdto.setEbelp(detail.getSortNum().toString());
      itemdto.setMenge(
          BigDecimalUtil.setScaleBigDecimalHalfUp(productDetail.getNum(), 3).toPlainString());
      itemdto.setAplfz(productDetail.getPurchaseDeliverTime() == null ? StrUtil.EMPTY
          : DateUtils.formatTimeStampToPureDate(productDetail.getPurchaseDeliverTime()));
      //海外类型添加运费供应商
      if (StrUtil.equals(param.getSupType(), Constants.SUPPLIERTYPE_ABROAD) || StrUtil.equals(
          param.getOrderType(), PurchaseOrderTypeEnum.OVER_SEAS.getKey())) {
        if (StrUtil.isNotBlank(productDetail.getFreightSupplierId()) || StrUtil.isNotBlank(
            productDetail.getTariffSupplierId()) || StrUtil.isNotBlank(
            productDetail.getIncidentalSupplierId())) {
          supplierRepository.findById(productDetail.getFreightSupplierId())
              .ifPresent(supplier -> {
                itemdto.setYfgys(supplier.getMdmCode());
                detail.setFreightSupplierId(supplier.getId());
                detail.setFreightSupplierName(supplier.getEnterpriseName());
              });
          supplierRepository.findById(productDetail.getTariffSupplierId())
              .ifPresent(supplier -> {
                itemdto.setGsgys(supplier.getMdmCode());
                detail.setTariffSupplierId(supplier.getId());
                detail.setTariffSupplierName(supplier.getEnterpriseName());
              });
          supplierRepository.findById(productDetail.getIncidentalSupplierId())
              .ifPresent(supplier -> {
                itemdto.setZfgys(supplier.getMdmCode());
                detail.setIncidentalSupplierId(supplier.getId());
                detail.setIncidentalSupplierName(supplier.getEnterpriseName());
              });
        } else {
          itemdto.setYfgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
          itemdto.setGsgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
          itemdto.setZfgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
        }
        itemdto.setZyyje(
            BigDecimalUtil.formatForStandard(productDetail.getFreight()).toPlainString());
        itemdto.setZgsje(
            BigDecimalUtil.formatForStandard(productDetail.getTariffAmount()).toPlainString());
        itemdto.setZjsj(
            BigDecimalUtil.formatForStandard(productDetail.getSettlementPrice()).toPlainString());
        itemdto.setZzf1(BigDecimalUtil.formatForStandard(productDetail.getIncidentalAmount())
            .toPlainString());
        detail.setTotalAmountIncludingTax(productDetail.getTotalAmountIncludingTax());
        detail.setPaymentAmount(productDetail.getPaymentAmount());
        detail.setTariffAmount(productDetail.getTariffAmount());
        detail.setIncidentalAmount(productDetail.getIncidentalAmount());
        detail.setProductRate(productDetail.getProductRate());
        detail.setSurcharge(productDetail.getSurcharge());
        detail.setFreight(productDetail.getFreight());
        detail.setSettlementPrice(
            Optional.ofNullable(productDetail.getSettlementPrice()).orElse(BigDecimal.ZERO));
        detail.setTotalSettlementPrice(
            Optional.ofNullable(productDetail.getTotalSettlementPrice()).orElse(BigDecimal.ZERO));
        //海外供应商添加货币汇率
        supplierOrder.setOrderRate(param.getOrderRate());
      }
      supplierOrderDetailV2Repository.save(detail);
      List<WWDTO> wwDto = new ArrayList<>();
      WWDTO ww = new WWDTO();
      wwDto.add(ww);
      itemdto.setWw(wwDto);
      items.add(itemdto);
    }
    String detailFormId = supplierOrderToForm.getId();
    List<SupplierOrderDetailV2> supplierOrderDetails =
        CollUtil.emptyIfNull(supplierOrderDetailV2Repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
            detailFormId, Constants.STATE_OK));
    // 是否应该更新入库进度
    BigDecimal detailNum =
        supplierOrderDetails
            .stream()
            .map(SupplierOrderDetailV2::getNum)
            .reduce(NumberUtil::add)
            .orElse(BigDecimal.ZERO);
    BigDecimal totalPrice = supplierOrderDetails.stream()
        .map(detail -> NumberUtil.mul(detail.getNum(), detail.getPrice())).reduce(NumberUtil::add)
        .orElse(BigDecimal.ZERO);
    BigDecimal totalFreight = supplierOrderDetails.stream()
        .map(SupplierOrderDetailV2::getFreight).filter(Objects::nonNull).reduce(NumberUtil::add).orElse(BigDecimal.ZERO);
    supplierOrder.setTotalNum(detailNum);
    supplierOrder.setPrice(totalPrice);
    // 保存总运费
    supplierOrder.setFreight(totalFreight);
    BigDecimal stockInputQty = supplierOrder.getTotalStockInputQty();


    BigDecimal totalCancel =
        supplierOrderToFormV2Repository.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
                SupplierOrderFormType.CANCEL.getType(), supplierOrder.getId(), Constants.STATE_OK)
            .stream()
            .map(SupplierOrderToFormV2::getNum)
            .reduce(NumberUtil::add)
            .orElse(BigDecimal.ZERO)
            .setScale(3, RoundingMode.HALF_UP);
    List<String> noStockProgressStatus =
        ListUtil.toList(
            SupplierOrderFormStatus.RETURN.getStatus(),
            SupplierOrderFormStatus.RETURN_COMPLETE.getStatus());
    BigDecimal totalReturn =
        supplierOrderToFormV2Repository.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
                SupplierOrderFormType.RETURN.getType(), supplierOrder.getId(), Constants.STATE_OK)
            .stream()
            .filter(
                supplierOrderToForm1 ->
                    noStockProgressStatus.contains(supplierOrderToForm1.getStatus()))
            .map(SupplierOrderToFormV2::getNum)
            .reduce(NumberUtil::add)
            .orElse(BigDecimal.ZERO)
            .setScale(3, RoundingMode.HALF_UP);
    //分母
    BigDecimal progress = BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
        NumberUtil.sub(supplierOrder.getTotalNum(), totalCancel, totalReturn), 3);
    //修改后入库进度分子分母相等，将订单状态调整为已完成
    if (NumberUtil.equals(stockInputQty, progress)) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
    }
    //入库进度：（入库数量合计-退库数量合计）/（订货数量合计-取消订货数量合计-退库数量合计），初始值为0/订单数量之和；
    //入库进度
    supplierOrder.makeAndSetStockProgress(stockInputQty, progress);
    //订货金额
    supplierOrder.setPrice(NumberUtil.sub(supplierOrder.getPrice(), allDifferencePrice));
    supplierOrderV2Repository.save(supplierOrder);
    // 构建sap-021接口(修改物料明细)
    try {
      sapService.sapPurchaseOrderWithAlarm(buildUpdateProductDetailParam(supplierOrder, items), "");
    } catch (Exception e) {
      throw new CheckException("调用 SAP-021 接口存在报错");
    }
  }

  private UpdatePurchaseOrderSapParam buildUpdateProductDetailParam(SupplierOrderV2 supplierOrder,
      List<ITEMDTO> items) {
    UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO dataDto = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headDto = new UpdatePurchaseOrderHEADDTO();
    headDto.setEbeln(supplierOrder.getCode());
    headDto.setZsp(Constants_Sap.CONFIRM_IDENTIFICATION);
    headDto.setZcdoa("N");
    headDto.setItem(items);
    dataDto.setHead(headDto);
    param.setData(dataDto);
    return param;
  }


  @DefaultSearchScheme(searchType = Constants.USER_PERMISSION_SUPPLIER_ORDER)
  @Override
  public void exportPurchaseOrderProduct(PurchaseOrderProductExport export, User user) {
    // 存储模版导出信息
    exportFiledTemplateEventProvider.exportTemplateEvent(this,export, Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER);
    Map<String, Object> queryMap = new HashMap<>();
    List<String> exportIds = new ArrayList<>();
    List<String> ids = export.getIds();
    Map<String, Object> params = new HashMap<>();
    boolean exportAll = false;
    if (CollUtil.isNotEmpty(ids)) {
      exportIds.addAll(ids);
      if (BeanUtil.isNotEmpty(export) && BooleanUtil.isTrue(export.getIsHistoricalOrder())) {
        params.put("isHistoricalOrder", true);
      }
    }else if (BeanUtil.isNotEmpty(export)) {
      export.setPageNo(1);
      export.setPageSize(Integer.MAX_VALUE);
      SearchPermission searchPermission =
          permissionTypeProvider.getSearchPermission(user, export.getUserGroup(),
              Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false);
      MergeUserPermission mergeUserPermission =
          permissionTypeProvider.mergePermission(searchPermission, new OperatorPermission());
      queryMap = export.toQueryMap(mergeUserPermission);
    } else {
      exportAll = true;
    }
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        "导出-采购单物料信息",
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        "",
        ""
    );
    missionRepository.save(mission);
    params.put("version", ShardingContext.getVersion());
    params.put("userId", user.getId());
    params.put("ids", exportIds);
    params.put("queryMap", queryMap);
    params.put("exportAll", exportAll);
    params.put("exportField", CollUtil.emptyIfNull(export.getSelectFieldList()));
    params.put("selectSupplier",
        StrUtil.isNotBlank(export.mGetQueryMapWithUnifiedFormValueStr(queryMap, "supplierName")));
    batchTaskMqSender.toHandleBatchTask(mission.getId(), JSON.toJSONString(params), Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER_PRODUCT_DETAIL);
  }

  @Override
  public List<PurchaseOrderProductV2DetailedVO> getPurchaseOrderProductDetailed(String id) {
    return CollUtil.emptyIfNull(
            supplierOrderDetailV2Repository.findAllByEntrustDetailIdAndState(id, Constants.STATE_OK))
        .stream()
        .map(PurchaseOrderProductV2DetailedVO::new)
        .collect(Collectors.toList());
  }
}
