package com.xhgj.srm.common.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: fanghuanxu
 * @Date: 2025/3/17 18:00
 * @Description: oms（履约）订单回款记录dto
 */
@Data
public class OmsOrderReceiptDto {

  @ApiModelProperty("id")
  @JSONField(name = "id")
  private String id;

  @ApiModelProperty("项目编号")
  @JSONField(name = "projectNo")
  private String projectNo;

  @ApiModelProperty("回款方式-0~25")
  @JSONField(name = "paymentType")
  private String paymentMethod;

  @ApiModelProperty("回款方式-对应SAP付款方式")
  // @JSONField(name = "paymentType")
  private String sapPaymentMethod;

  @ApiModelProperty("回款方式中文")
  private String paymentTypeName;

  public String getPaymentTypeName() {
    return StrUtil.isBlank(paymentMethod) ? null
        : OrderReceiptPaymentMethodEnum.getDescFromCode(paymentMethod);
  }

  public String getSapPaymentMethod() {
    PayTypeSAPEnums sapEnumFromCode =
        OrderReceiptPaymentMethodEnum.getSapEnumFromCode(paymentMethod);
    return sapEnumFromCode == null ? null : sapEnumFromCode.getCode();
  }



  @ApiModelProperty("回款类型-A、B、C")
  @JSONField(name = "type")
  private String paymentType;

  @ApiModelProperty("银行交易流水号")
  @JSONField(name = "serialNumber")
  private String bankSerialNo;

  @ApiModelProperty("汇票号")
  @JSONField(name = "billNo")
  private String billNo;

  @ApiModelProperty("核销时间")
  @JSONField(name = "writeOffTime",format = "yyyy-MM-dd")
  private Date clearingTime;

  public Long getClearingTime() {
    return clearingTime == null ? null : clearingTime.getTime();
  }

  @ApiModelProperty("到账时间")
  @JSONField(name = "time",format = "yyyy-MM-dd")
  private Date receivedTime;

  public Long getReceivedTime() {
    return receivedTime == null ? null : receivedTime.getTime();
  }

  @ApiModelProperty("回款比例(永远为正数)")
  @JSONField(name = "paymentProportion")
  private BigDecimal paymentRatio;

  @ApiModelProperty("回款金额(可能为负)")
  @JSONField(name = "money")
  private BigDecimal paymentAmount;

}
