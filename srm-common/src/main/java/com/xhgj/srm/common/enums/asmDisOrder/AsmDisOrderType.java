package com.xhgj.srm.common.enums.asmDisOrder;/**
 * @since 2025/3/4 9:41
 */

/**
 *<AUTHOR>
 *@date 2025/3/4 09:41:24
 *@description
 */
public enum AsmDisOrderType {
  /**
   * 1.组装单
   */
  ASSEMBLY_ORDER((byte) 1, "组装单"),
  /**
   * 2.拆卸单
   */
  DISASSEMBLY_ORDER((byte) 2, "拆卸单")
  ;

  private byte code;

  private String name;

  AsmDisOrderType(byte code, String name) {
    this.code = code;
    this.name = name;
  }

  public static String getNameByCode(byte code) {
    for (AsmDisOrderType value : AsmDisOrderType.values()) {
      if (value.getCode() == code) {
        return value.getName();
      }
    }
    return null;
  }

  public byte getCode() {
    return code;
  }

  public String getName() {
    return name;
  }
}
