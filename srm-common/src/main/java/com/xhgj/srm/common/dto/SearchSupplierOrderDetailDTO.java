package com.xhgj.srm.common.dto;/**
 * @since 2025/3/7 10:22
 */

import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 *<AUTHOR>
 *@date 2025/3/7 10:22:27
 *@description 对于sap仓库查询没有价格情况，从供应商订单 入库单中获取未税价格
 */
@Data
public class SearchSupplierOrderDetailDTO {

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 批次号
   */
  private String batchNo;

  /**
   * 价格
   */
  private BigDecimal price;

  /**
   * 税率
   */
  private BigDecimal tax;

  /**
   * 未税单价
   */
  private BigDecimal netPrice;

  public BigDecimal getNetPrice() {
    if (price == null || tax == null) {
      return BigDecimal.ZERO;
    }
    return price.divide(tax.add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
  }

}
