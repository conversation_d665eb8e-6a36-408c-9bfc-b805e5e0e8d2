package com.xhgj.srm.v2.service.impl.purchaseOrder;/**
 * @since 2025/4/28 9:40
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.component.LockUtils;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.dto.ApprovalWarehouseExtra;
import com.xhgj.srm.common.dto.ApprovalWarehouseExtra.ApprovalWarehouseExtraDetail;
import com.xhgj.srm.common.dto.ExpressCompanyDTO;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.common.utils.supplierOrderForm.SupplierOrderFormCodeGenerator;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderWarehousingStatistics;
import com.xhgj.srm.jpa.entity.BaseSupplierOrderDetail;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.jpa.util.LazyLoaderContext;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult.ReturnMessage;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.v2.constants.PurchaseOrderV2Lock;
import com.xhgj.srm.v2.constants.SupplierOrderFormSource;
import com.xhgj.srm.v2.dao.SupplierOrderToFormV2Dao;
import com.xhgj.srm.v2.dto.InputInvoiceOrderWithDetailV2;
import com.xhgj.srm.v2.dto.PurchaseOrderInvoiceRelationV2;
import com.xhgj.srm.v2.dto.PurchaseOrderWarehousingV2DTO;
import com.xhgj.srm.v2.dto.WarehouseEntryListParamV2;
import com.xhgj.srm.v2.dto.WarehousingV2DTO;
import com.xhgj.srm.v2.factory.PurchaseOrderWarehouseV2Factory;
import com.xhgj.srm.v2.factory.SapV2Factory;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseApplyV2AddFormSap.PurchaseOrderWarehouseApplyV2AddFormSapDetail;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddForm;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddForm.PurchaseOrderWarehouseAddFormDetail;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddFormSap;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2UpdateForm;
import com.xhgj.srm.v2.form.WarehouseEntryListV2Params;
import com.xhgj.srm.v2.provider.PermissionTypeProvider;
import com.xhgj.srm.v2.provider.ShareInputInvoiceProvider;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderProductV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2BaseService;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2WarehouseService;
import com.xhgj.srm.v2.vo.purchaseOrder.form.WarehouseFormDetailVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PurchaseOrderV2WarehouseServiceImpl implements PurchaseOrderV2WarehouseService {

  @Resource
  LockUtils lockUtils;
  @Resource
  private PlatformTransactionManager transactionManager;
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;
  @Resource
  private PurchaseOrderWarehouseV2Factory purchaseOrderWarehouseV2Factory;
  @Resource
  private ShareInputInvoiceProvider shareInputInvoiceProvider;
  @Resource
  private HZeroService hZeroService;
  @Resource
  private PurchaseOrderV2BaseService purchaseOrderV2BaseService;
  @Resource
  private SapV2Factory sapV2Factory;
  @Resource
  private SAPService sapService;
  @Resource
  private RedisUtil redisUtil;
  @Resource
  private UserRepository userRepository;
  @Resource
  private PermissionTypeProvider permissionTypeProvider;
  @Resource
  private SupplierOrderToFormV2Dao supplierOrderToFormDao;
  @Resource
  SupplierOrderProductV2Repository supplierOrderProductV2Repository;
  @Resource
  HttpUtil httpUtil;
  @Resource
  InventoryLocationRepository inventoryLocationRepository;
  @Autowired
  private MissionDispatcher missionDispatcher;
  @Resource private MissionRepository missionRepository;
  @Resource private MissionUtil missionUtil;

  @Override
  public void warehouseAdd(PurchaseOrderWarehouseV2AddForm form, User user) {
    // 采购订单id
    String id = form.getPurchaseOrderId();
    List<RLock> rLocks = lockUtils.lockAll(new HashSet<>(Collections.singleton(id)),
        PurchaseOrderV2Lock.PURCHASE_ORDER_WAREHOUSE_LOCK);
    List<RLock> rLocks2 = lockUtils.lockAll(new HashSet<>(Collections.singleton(id)),
        PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        TransactionSynchronizationManager.registerSynchronization(
            new TransactionSynchronizationAdapter(){
              @Override
              public void afterCompletion(int status) {
                SupplierOrderFormCodeGenerator.clear();
              }
            }
        );
        List<String> detailIds = form.getProductDetailList().stream()
            .map(PurchaseOrderWarehouseAddFormDetail::getId).collect(Collectors.toList());
        // 创建ID到索引的映射，用于排序
        Map<String, Integer> idToIndexMap = new HashMap<>();
        for (int i = 0; i < detailIds.size(); i++) {
          idToIndexMap.put(detailIds.get(i), i);
        }
        List<SupplierOrderDetailV2> inApplyDetails = supplierOrderDetailV2Repository.findAllById(detailIds);
        // 对details进行排序，使其与表单中的顺序一致
        inApplyDetails.sort(
            Comparator.comparing(detail -> idToIndexMap.getOrDefault(detail.getId(), Integer.MAX_VALUE)));
        // -------------基础校验----------------
        // #check 判断采购订单是否存在
        SupplierOrderV2 supplierOrder = supplierOrderV2Repository.findById(id).orElseThrow(() -> new CheckException("采购订单不存在"));
        // -------------数据生成----------------
          // 0.查询出入库申请单form
        SupplierOrderToFormV2 inApplyForm = supplierOrderToFormV2Repository.findById(form.getWarehouseApplyId()).orElseThrow(() -> new CheckException("入库申请单不存在"));
          // 1.生成入库单form
        SupplierOrderToFormV2 inForm =
            purchaseOrderWarehouseV2Factory.createWarehouseForm(form, inApplyForm, inApplyDetails, user);
        supplierOrderToFormV2Repository.save(inForm);
        // 2.生成入库单明细
        List<SupplierOrderDetailV2> inDetails =
            purchaseOrderWarehouseV2Factory.createWarehouseFormDetail(form, inApplyForm, inForm,
                inApplyDetails);
        supplierOrderDetailV2Repository.saveAll(inDetails);
        // -------------调用飞搭流程(需要异步+整体事务提交)----------------
        // 如果来源是SAP则不需要飞搭
        if (SupplierOrderFormSource.SAP.equals(inApplyForm.getSource()) && form instanceof PurchaseOrderWarehouseV2AddFormSap) {
          inForm.setReviewStatus(SupplierOrderFormReviewStatus.NORMAL.getCode());
          inForm.setProductVoucher(((PurchaseOrderWarehouseV2AddFormSap) form).getProductVoucher());
          inForm.setProductVoucherYear(((PurchaseOrderWarehouseV2AddFormSap) form).getProductVoucherYear());
          List<PurchaseOrderWarehouseApplyV2AddFormSapDetail> productInfoList = ((PurchaseOrderWarehouseV2AddFormSap) form).getProductInfoList();
          Map<String, PurchaseOrderWarehouseApplyV2AddFormSapDetail> productInfoMap =
              productInfoList.stream().collect(Collectors.toMap(PurchaseOrderWarehouseApplyV2AddFormSapDetail::getPurchaseOrderRowId, item -> item));
          for (SupplierOrderDetailV2 inDetail : inDetails) {
            PurchaseOrderWarehouseApplyV2AddFormSapDetail productInfo = productInfoMap.get(inDetail.getSortNum().toString());
            if (productInfo != null) {
              inDetail.setBatchNo(productInfo.getBatchNo());
              inDetail.setSapRowId(productInfo.getWarehousingRowId());
            }
          }
          // 模拟入库单审核通过
          this.simulatedWarehousingApproved(supplierOrder, inForm, inDetails, inApplyDetails);
        } else {
          String traceId = MDC.get("TRACE_ID");
          TransactionSynchronizationManager.registerSynchronization(
              new TransactionSynchronizationAdapter(){
                @Override
                public void afterCommit() {
                  // 事务提交后执行异步任务
                  CompletableFuture.runAsync(() -> {
                    MDC.put("TRACE_ID", traceId);
                    try {
                      syncFeida(supplierOrder, inForm, inDetails, user);
                    } catch (Exception e) {
                      log.error("入库单飞搭审核流程创建失败", e);
                    }
                  });
                }
              }
          );
        }
        return null;
      });
    }catch (Exception e) {
      SupplierOrderFormCodeGenerator.INSTANCE.rollbackOrderNumber();
      throw e;
    } finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }
  }

  /**
   * 模拟入库单审核通过
   * @param supplierOrder
   * @param inDetails
   */
  private void simulatedWarehousingApproved(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inForm,
      List<SupplierOrderDetailV2> inDetails, List<SupplierOrderDetailV2> inApplyDetails) {
    Map<String, SupplierOrderDetailV2> inApplyDetailMap =
        inApplyDetails.stream().collect(Collectors.toMap(SupplierOrderDetailV2::getDetailedId, item -> item));
    List<SupplierOrderDetailV2> details = new ArrayList<>();
    for (SupplierOrderDetailV2 inDetail : inDetails) {
      // 更新inDetail的信息
      BigDecimal cancelQty = BigDecimal.ZERO;
      BigDecimal stockInputQty = inDetail.getStockInputQty();
      // todo 可能有特殊情况
      // 免费行和寄售的行数量设置为0
      if (inDetail.getFreeState().equals(SimpleBooleanEnum.YES.getKey())
          || inDetail.getProjectType().equals(Constants.PROJECT_TYPE_JS)) {
        inDetail.setInvoicableNum(BigDecimal.ZERO);
      }

      String detailedId = inDetail.getDetailedId();
      SupplierOrderDetailV2 detail = inDetail.getDetailed();
      details.add(detail);
      SupplierOrderDetailV2 inApplyDetail = inApplyDetailMap.get(detailedId);
      if (inApplyDetail == null) {
        throw new CheckException("入库申请单明细不存在");
      }
      // @物料明细 待申请入库数量++
      detail.setWaitQty(NumberUtil.add(detail.getWaitQty(), cancelQty));
      // @物料明细 已申请入库数量--
      detail.setShipQty(NumberUtil.sub(detail.getShipQty(), cancelQty));
      // @物料明细 入库数量++
      detail.setStockInputQty(NumberUtil.add(detail.getStockInputQty(), stockInputQty));
      // @物料明细 实际交货++
      detail.setSettleQty(NumberUtil.add(detail.getSettleQty(), stockInputQty));
      // @入库申请单 已入库数量++
      inApplyDetail.setStockInputQty(NumberUtil.add(inApplyDetail.getStockInputQty(), stockInputQty));
      // @入库申请单 取消入库数量++
      inApplyDetail.setCancelQty(NumberUtil.add(inApplyDetail.getCancelQty(), cancelQty));
    }
    supplierOrderDetailV2Repository.saveAll(inApplyDetails);
    supplierOrderDetailV2Repository.saveAll(details);
    supplierOrderDetailV2Repository.saveAll(inDetails);
    supplierOrderDetailV2Repository.flush();
    supplierOrderToFormV2Repository.saveAndFlush(inForm);
    // （入库数量合计-退库数量合计）/（订货数量合计-取消订货数量合计-退库数量合计）
    String progress = purchaseOrderV2BaseService.setStockProgress(supplierOrder);
    supplierOrder.setStockProgress(progress);
    if (NumberUtil.isGreaterOrEqual(
        supplierOrder.getTotalStockInputQty(), supplierOrder.getTotalNum())) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
    } else {
      supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getOrderState());
    }
    supplierOrderV2Repository.saveAndFlush(supplierOrder);
  }

  @Override
  public void warehouseUpdate(PurchaseOrderWarehouseV2UpdateForm form) {
    String id = form.getPurchaseOrderId();
    List<RLock> rLocks = lockUtils.lockAll(new HashSet<>(Collections.singleton(id)),
        PurchaseOrderV2Lock.PURCHASE_ORDER_WAREHOUSE_LOCK);
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        // 修改入库单的快递信息
        SupplierOrderToFormV2 inForm = supplierOrderToFormV2Repository.findById(form.getId())
            .orElseThrow(() -> new CheckException("入库单不存在"));
        // 判断入库单，需要未冲销且有SAP物料凭证号
        if (SupplierOrderFormStatus.REVERSAL.getStatus().equals(inForm.getStatus()) || StrUtil.isBlank(inForm.getProductVoucher())) {
          throw new CheckException("冲销的和没有SAP物料凭证号的入库单不允许修改");
        }
        SupplierOrderV2 supplierOrder =
            supplierOrderV2Repository.findById(inForm.getSupplierOrderId())
                .orElseThrow(() -> new CheckException("采购订单不存在"));
        List<SupplierOrderDetailV2> inDetails =
            supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
                Collections.singletonList(inForm.getId()), Constants.STATE_OK);
        inForm.setLogisticsCompany(form.getLogisticsCompany());
        inForm.setTrackNum(form.getTrackNum());
        inForm.setLogisticsCode(form.getLogisticsCode());
        inForm.setCode(form.getCode());
        inForm.setUpdateTime(System.currentTimeMillis());
        supplierOrderToFormV2Repository.save(inForm);
        ReceiptVoucherSynchronizationParam mm031ForWarehouseUpdate = sapV2Factory.createMM031ForWarehouseUpdate(supplierOrder, inForm, inDetails);
        ReceiptVoucherSynchronizationResult result;
        try {
          result = sapService.sapMaterialVoucherWithLockGroup(mm031ForWarehouseUpdate);
        } catch (CheckException e){
          throw e;
        }catch (Exception e) {
          log.error(ExceptionUtil.stacktraceToString(e));
          throw new CheckException("SAP系统响应异常，请联系管理员！");
        }
        List<ReturnMessage> returnMessages = result.getReturnMessages();
        for (ReturnMessage returnMessage : returnMessages) {
          String lineItem = returnMessage.getLineItem();
          String documentNumber = returnMessage.getDocumentNumber();
          String purchaseOrderLineItems = returnMessage.getPurchaseOrderLineItems();
          inForm.setProductVoucher(documentNumber);
          if (StrUtil.isNotBlank(purchaseOrderLineItems)) {
            Integer purchaseOrderLineItems1 = Integer.valueOf(purchaseOrderLineItems);
            Optional<SupplierOrderDetailV2> first =
                inDetails.stream().filter(supplierOrderDetail -> Objects.equals(supplierOrderDetail.getSortNum(), purchaseOrderLineItems1)).findFirst();
            first.ifPresent(supplierOrderDetail -> {
              supplierOrderDetail.setBatchNo(returnMessage.getCharge());
              supplierOrderDetail.setSapRowId(lineItem);
              supplierOrderDetailV2Repository.save(supplierOrderDetail);
            });
          }
        }
        return null;
      });
    } finally {
      lockUtils.unlockAllLocks(rLocks);
    }
  }

  @Override
  public List<WarehouseFormDetailVO> getWarehouseDetail(String supplierOrderId) {
    // 根据采购订单id查询入库申请单
    List<SupplierOrderToFormV2> warehouseList =
    supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeAndState(supplierOrderId, SupplierOrderFormType.WAREHOUSING.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(warehouseList)) {
      return Collections.emptyList();
    }
    // 查询入库申请单明细
    List<String> warehouseIds = warehouseList.stream().map(SupplierOrderToFormV2::getId).collect(Collectors.toList());
    List<SupplierOrderDetailV2> warehouseDetails = supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(warehouseIds, Constants.STATE_OK);
    List<String> detailIds = warehouseDetails.stream().map(SupplierOrderDetailV2::getId).collect(Collectors.toList());
    detailIds.add("-1");
    // 查询发票号
    List<InputInvoiceOrderWithDetailV2> inputInvoices = shareInputInvoiceProvider.getOrderInvoiceRelationListByDetailIdsRef(detailIds);
    Map<String, List<SupplierOrderDetailV2>> warehouseDetailsMap = warehouseDetails.stream()
        .collect(Collectors.groupingBy(SupplierOrderDetailV2::getOrderToFormId));
    return purchaseOrderWarehouseV2Factory.buildDetailVOS(warehouseList, warehouseDetailsMap, inputInvoices);
  }

  @Override
  public void warehouseReversal(String supplierOrderFormId) {
    SupplierOrderToFormV2 inForm =
        supplierOrderToFormV2Repository.findById(supplierOrderFormId)
            .orElseThrow(() -> new CheckException("入库单不存在"));
    SupplierOrderV2 supplierOrder = supplierOrderV2Repository
        .findById(inForm.getSupplierOrderId())
            .orElseThrow(() -> new CheckException("采购订单不存在"));
    List<RLock> rLocks =
        lockUtils.lockAll(new HashSet<>(Collections.singleton(inForm.getSupplierOrderId())),
            PurchaseOrderV2Lock.PURCHASE_ORDER_WAREHOUSE_LOCK);
    List<RLock> rLocks2 =
        lockUtils.lockAll(new HashSet<>(Collections.singleton(inForm.getSupplierOrderId())),
            PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        this.warehouseReversalBase(supplierOrder, inForm);
        this.sapReversalSync(supplierOrder, inForm);
        return null;
      });
    } finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }
  }

  @Override
  public void warehouseReversal(String sapVoucherNo, String sapReversalVoucherNo, User user) {
    // 通过sap凭证号查询入库申请单
    SupplierOrderToFormV2 inForm =
        supplierOrderToFormV2Repository.findFirstByProductVoucherAndState(sapVoucherNo,
            Constants.STATE_OK);
    if (inForm == null) {
      throw new CheckException("入库单不存在");
    }
    // 查询采购订单
    SupplierOrderV2 supplierOrder =
        supplierOrderV2Repository.findById(inForm.getSupplierOrderId())
            .orElseThrow(() -> new CheckException("采购订单不存在"));
    List<RLock> rLocks =
        lockUtils.lockAll(new HashSet<>(Collections.singleton(inForm.getSupplierOrderId())),
            PurchaseOrderV2Lock.PURCHASE_ORDER_WAREHOUSE_LOCK);
    List<RLock> rLocks2 =
        lockUtils.lockAll(new HashSet<>(Collections.singleton(inForm.getSupplierOrderId())),
            PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        this.warehouseReversalBase(supplierOrder, inForm);
        inForm.setSapReversalNo(sapReversalVoucherNo);
        supplierOrderToFormV2Repository.saveAndFlush(inForm);
        return null;
      });
    } finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }
  }

  private void warehouseReversalBase(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inForm) {
    // 0.入库单明细 、 入库申请单明细、物料明细
    List<SupplierOrderDetailV2> inDetails = LazyLoaderContext.lazyLoad(
        () -> supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
            Collections.singletonList(inForm.getId()), Constants.STATE_OK));
    List<String> detailIds = inDetails.stream().map(BaseSupplierOrderDetail::getDetailedId).distinct()
        .collect(Collectors.toList());
    detailIds.add("-1");
    // 1. 校验
    purchaseOrderWarehouseV2Factory.checkWarehouseReversal(inForm, supplierOrder, inDetails);
    // 2.修改入库单状态
    inForm.setStatus(SupplierOrderFormStatus.REVERSAL.getStatus());
    inForm.setUpdateTime(System.currentTimeMillis());
    supplierOrderToFormV2Repository.saveAndFlush(inForm);
    List<SupplierOrderDetailV2> details =
        LazyLoaderContext.lazyLoad(() -> supplierOrderDetailV2Repository.findAllById(detailIds));
    Map<String, SupplierOrderDetailV2> detailMap = details.stream()
        .collect(Collectors.toMap(SupplierOrderDetailV2::getId, item -> item));
    // 查询入库申请单明细
    String inWareHouseApplyId = inDetails.get(0).getInWareHouseApplyId();
    List<SupplierOrderDetailV2> inApplyDetails = LazyLoaderContext.lazyLoad(
        () -> supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
            Collections.singletonList(inWareHouseApplyId), Constants.STATE_OK));
    Map<String, SupplierOrderDetailV2> inApplyDetailMap = inApplyDetails.stream()
        .collect(Collectors.toMap(SupplierOrderDetailV2::getDetailedId, item -> item));
    for (SupplierOrderDetailV2 inDetail : inDetails) {
      String detailedId = inDetail.getDetailedId();
      SupplierOrderDetailV2 detail = detailMap.get(detailedId);
      SupplierOrderDetailV2 inApplyDetail = inApplyDetailMap.get(detailedId);
      // @物料明细 待申请入库数量++
      detail.setWaitQty(NumberUtil.add(detail.getWaitQty(), inDetail.getStockInputQty()));
      // @物料明细 已申请入库数量--
      detail.setShipQty(NumberUtil.sub(detail.getShipQty(), inDetail.getStockInputQty()));
      // @物料明细 入库数量 --
      detail.setStockInputQty(NumberUtil.sub(detail.getStockInputQty(), inDetail.getStockInputQty()));
      // @物料明细 实际交货数量 --
      detail.setSettleQty(NumberUtil.sub(detail.getSettleQty(), inDetail.getStockInputQty()));
      // @入库申请单 取消入库数量 ++
      inApplyDetail.setCancelQty(NumberUtil.add(inApplyDetail.getCancelQty(), inDetail.getStockInputQty()));
    }
    supplierOrderDetailV2Repository.saveAll(inApplyDetails);
    supplierOrderDetailV2Repository.saveAll(details);
    supplierOrderDetailV2Repository.flush();
    // 3.修改采购订单入库进度
    // （入库数量合计-退库数量合计）/（订货数量合计-取消订货数量合计-退库数量合计）
    String progress = purchaseOrderV2BaseService.setStockProgress(supplierOrder);
    supplierOrder.setStockProgress(progress);
    if (NumberUtil.isGreaterOrEqual(
        supplierOrder.getTotalStockInputQty(), supplierOrder.getTotalNum())) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
    } else {
      supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getOrderState());
    }
    supplierOrderV2Repository.saveAndFlush(supplierOrder);
  }

  private void sapReversalSync(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inForm) {
    // 调用 SAP 逻辑
    ReceiptOrReturnReversalParams params =
        sapV2Factory.create032ReversalParamsForWarehouse(supplierOrder, inForm);
    try {
      ReceiptOrReturnReversalResult receiptOrReturnReversalResult = sapService.sapReceiptOrReturnReversal(params);
      String reversalNo = receiptOrReturnReversalResult.getReturnX().getBelnr();
      inForm.setSapReversalNo(reversalNo);
      supplierOrderToFormV2Repository.saveAndFlush(inForm);
    } catch (Exception e) {
      throw e;
    }
  }

  @Override
  public void syncFeida(String supplierOrderFormId, User user) {
    // 通过formId查询入库申请单
    SupplierOrderToFormV2 inApplyForm =
        supplierOrderToFormV2Repository.findById(supplierOrderFormId)
            .orElseThrow(() -> new CheckException("入库单不存在"));
    // 判断是否调用失败
    if (!SupplierOrderFormCallStatus.CALL_FAIL.getStatus().equals(inApplyForm.getCallStatus())) {
      throw new CheckException("推送三方失败的入库单才可以重试");
    }
    // 查询采购订单
    SupplierOrderV2 supplierOrder =
        supplierOrderV2Repository.findById(inApplyForm.getSupplierOrderId())
            .orElseThrow(() -> new CheckException("采购订单不存在"));
    // 通过formId查询入库申请单明细
    List<SupplierOrderDetailV2> inApplyDetails =
        supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
            Collections.singletonList(inApplyForm.getId()), Constants.STATE_OK);
    this.syncFeida(supplierOrder, inApplyForm, inApplyDetails, user);
  }

  private void syncFeida(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inForm, List<SupplierOrderDetailV2> inDetails, User user) {
    try {
      StartProcessParam feidaProcessParam =
          purchaseOrderWarehouseV2Factory.createFeidaProcessParam(supplierOrder, inForm, inDetails,
              user);
      StartProcessVo startProcessVo = hZeroService.startProcessWithoutFile(feidaProcessParam);
      inForm.setCallStatus(SupplierOrderFormCallStatus.CALL_SUCCESS.getStatus());
      inForm.setReviewId(startProcessVo.getInstanceId());
    } catch (Exception e) {
      inForm.setCallStatus(SupplierOrderFormCallStatus.CALL_FAIL.getStatus());
      throw e;
    }finally {
      inForm.setCallTime(System.currentTimeMillis());
      inForm.setUpdateTime(System.currentTimeMillis());
      supplierOrderToFormV2Repository.saveAndFlush(inForm);
    }
  }

  public void sapSync(String informId) {
    // 通过formId查询入库申请单
    SupplierOrderToFormV2 inForm =
        supplierOrderToFormV2Repository.findById(informId)
            .orElseThrow(() -> new CheckException("入库单不存在"));
    // 查询采购订单
    SupplierOrderV2 supplierOrder =
        supplierOrderV2Repository.findById(inForm.getSupplierOrderId())
            .orElseThrow(() -> new CheckException("采购订单不存在"));
    // 通过formId查询入库申请单明细
    List<SupplierOrderDetailV2> inDetails =
        supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
            Collections.singletonList(inForm.getId()), Constants.STATE_OK);
    this.sapSync(supplierOrder, inForm, inDetails);
  }

  /**
   * sap同步收货过账 031接口
   */
  private void sapSync(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inForm,
      List<SupplierOrderDetailV2> inDetails) {
    ReceiptVoucherSynchronizationParam mm031ForWarehouseCreate =
        sapV2Factory.createMM031ForWarehouseCreate(supplierOrder, inForm, inDetails);
    ReceiptVoucherSynchronizationResult result;
    try {
      result = sapService.sapMaterialVoucherWithLockGroup(mm031ForWarehouseCreate);
    } catch (CheckException e){
      throw e;
    }catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      throw new CheckException("SAP系统响应异常，请联系管理员！");
    }
    List<ReturnMessage> returnMessages = result.getReturnMessages();
    for (ReturnMessage returnMessage : returnMessages) {
      String lineItem = returnMessage.getLineItem();
      String documentNumber = returnMessage.getDocumentNumber();
      String purchaseOrderLineItems = returnMessage.getPurchaseOrderLineItems();
      inForm.setProductVoucher(documentNumber);
      if (StrUtil.isNotBlank(purchaseOrderLineItems)) {
        Integer purchaseOrderLineItems1 = Integer.valueOf(purchaseOrderLineItems);
        Optional<SupplierOrderDetailV2> first =
            inDetails.stream().filter(inDetail -> Objects.equals(inDetail.getSortNum(), purchaseOrderLineItems1)).findFirst();
        first.ifPresent(supplierOrderDetail -> {
          supplierOrderDetail.setBatchNo(returnMessage.getCharge());
          supplierOrderDetail.setSapRowId(lineItem);
        });
      }
    }
    supplierOrderDetailV2Repository.saveAll(inDetails);
    supplierOrderDetailV2Repository.flush();
    // 更新入库单
    supplierOrderToFormV2Repository.saveAndFlush(inForm);
  }

  @Override
  public void auditCallBack(ApprovalResult approvalResult, SupplierOrderFormReviewStatus status) {
    String reason = approvalResult.getRemark();
    String reviewId = approvalResult.getProcessInstanceId();
    String extraJson = approvalResult.getExtraJson();
    ApprovalWarehouseExtra approvalWarehouseExtra = JSON.parseObject(extraJson, ApprovalWarehouseExtra.class);
    SupplierOrderToFormV2 inForm =
        supplierOrderToFormV2Repository.findFirstByReviewIdAndState(reviewId, Constants.STATE_OK)
            .orElseThrow(() -> new CheckException("入库单不存在"));
    SupplierOrderV2 supplierOrder =
        supplierOrderV2Repository.findById(inForm.getSupplierOrderId())
            .orElseThrow(() -> new CheckException("采购订单不存在"));
    List<RLock> rLocks = lockUtils.lockAll(inForm.getId(), PurchaseOrderV2Lock.PURCHASE_ORDER_WAREHOUSE_LOCK);
    List<RLock> rLocks2 = lockUtils.lockAll(supplierOrder.getId(), PurchaseOrderV2Lock.PURCHASE_ORDER_LOCK);
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(s -> {
        // 如果状态不是飞搭审核中，则不处理
        if (!inForm.getReviewStatus().equals(SupplierOrderFormReviewStatus.FEI_DA_AUDIT.getCode())) {
          return null;
        }
        List<SupplierOrderDetailV2> inDetails = LazyLoaderContext.lazyLoad(
            () -> supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
                Collections.singletonList(inForm.getId()), Constants.STATE_OK));
        List<String> detailIds = inDetails.stream().map(BaseSupplierOrderDetail::getDetailedId).distinct()
            .collect(Collectors.toList());
        detailIds.add("-1");
        List<SupplierOrderDetailV2> details =
            LazyLoaderContext.lazyLoad(() -> supplierOrderDetailV2Repository.findAllById(detailIds));
        // 查询物料明细
        List<String> productIds = details.stream().map(BaseSupplierOrderDetail::getOrderProductId)
            .collect(Collectors.toList());
        productIds.add("-1");
        List<SupplierOrderProductV2> supplierOrderProductV2List =
            supplierOrderProductV2Repository.findAllById(productIds);
        Map<String, SupplierOrderProductV2> productMap =
            supplierOrderProductV2List.stream().collect(Collectors.toMap(SupplierOrderProductV2::getId,
                item -> item));
        // 相关detail填充productMap
        details.forEach(item -> {
          SupplierOrderProductV2 product = productMap.get(item.getOrderProductId());
          if (product != null) {
            item.setSupplierOrderProduct(product);
          }
        });
        Map<String, SupplierOrderDetailV2> detailMap = details.stream()
            .collect(Collectors.toMap(SupplierOrderDetailV2::getId, item -> item));
        // 查询入库申请单明细
        String inWareHouseApplyId = inDetails.get(0).getInWareHouseApplyId();
        // 查询入库申请单
        SupplierOrderToFormV2 inApplyForm =
            supplierOrderToFormV2Repository.findById(inWareHouseApplyId)
                .orElseThrow(() -> new CheckException("入库申请单不存在"));
        List<SupplierOrderDetailV2> inApplyDetails = LazyLoaderContext.lazyLoad(() -> supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
                Collections.singletonList(inWareHouseApplyId), Constants.STATE_OK));
        // 相关detail填充productMap
        inApplyDetails.forEach(item -> {
          SupplierOrderProductV2 product = productMap.get(item.getOrderProductId());
          if (product != null) {
            item.setSupplierOrderProduct(product);
          }
        });
        // 相关detail填充productMap
        inDetails.forEach(item -> {
          SupplierOrderProductV2 product = productMap.get(item.getOrderProductId());
          if (product != null) {
            item.setSupplierOrderProduct(product);
          }
        });
        Map<String, SupplierOrderDetailV2> inApplyDetailMap = inApplyDetails.stream()
            .collect(Collectors.toMap(SupplierOrderDetailV2::getDetailedId, item -> item));
        if (CollUtil.isEmpty(inDetails)) {
          throw new CheckException("入库单明细不存在");
        }
        // 飞搭审核失败
        if (SupplierOrderFormReviewStatus.FEI_DA_REJECT == status) {
          inForm.setReviewStatus(SupplierOrderFormReviewStatus.FEI_DA_REJECT.getCode());
          inForm.setReviewReason(reason);
          inForm.setUpdateTime(System.currentTimeMillis());
          for (SupplierOrderDetailV2 inDetail : inDetails) {
            String detailedId = inDetail.getDetailedId();
            SupplierOrderDetailV2 detail = detailMap.get(detailedId);
            SupplierOrderDetailV2 inApplyDetail = inApplyDetailMap.get(detailedId);
            // @物料明细 待申请入库数量 ++
            detail.setWaitQty(NumberUtil.add(detail.getWaitQty(), inDetail.getStockInputQty()));
            // @物料明细 已申请入库数量 --
            detail.setShipQty(NumberUtil.sub(detail.getShipQty(), inDetail.getStockInputQty()));
            // @入库申请单 取消入库数量 ++
            inApplyDetail.setCancelQty(NumberUtil.add(inApplyDetail.getCancelQty(), inDetail.getStockInputQty()));
          }
          supplierOrderDetailV2Repository.saveAll(inApplyDetails);
          supplierOrderDetailV2Repository.saveAll(details);
          supplierOrderDetailV2Repository.flush();
          supplierOrderToFormV2Repository.saveAndFlush(inForm);
        }
        // 飞搭审核通过
        if (SupplierOrderFormReviewStatus.NORMAL == status) {
          inForm.setReviewStatus(SupplierOrderFormReviewStatus.NORMAL.getCode());
          inForm.setReviewReason(reason);
          inForm.setUpdateTime(System.currentTimeMillis());
          inApplyForm.setUpdateTime(System.currentTimeMillis());
          inApplyForm.setWarehousing(true);
          // 更新入库单信息
          if (approvalWarehouseExtra.getPostingDate() != null) {
            inForm.setPostingDate(approvalWarehouseExtra.getPostingDate());
            inApplyForm.setPostingDate(approvalWarehouseExtra.getPostingDate());
          }
          if (StrUtil.isNotBlank(approvalWarehouseExtra.getLogisticsCompanyCode())
              && !approvalWarehouseExtra.getLogisticsCompanyCode().equals(inForm.getLogisticsCode())) {
            inForm.setLogisticsCode(approvalWarehouseExtra.getLogisticsCompanyCode());
            inApplyForm.setLogisticsCode(approvalWarehouseExtra.getLogisticsCompanyCode());
            List<ExpressCompanyDTO> logisticsCompanies =
                httpUtil.getLogisticsCompanies(approvalWarehouseExtra.getLogisticsCompanyCode());
            if (CollUtil.isNotEmpty(logisticsCompanies)) {
              ExpressCompanyDTO expressCompanyDTO = logisticsCompanies.get(0);
              inForm.setLogisticsCompany(expressCompanyDTO.getExpressCompany());
              inApplyForm.setLogisticsCompany(expressCompanyDTO.getExpressCompany());
            }
          }
          if (StrUtil.isNotBlank(approvalWarehouseExtra.getExpressNo())) {
            inForm.setTrackNum(approvalWarehouseExtra.getExpressNo());
            inApplyForm.setTrackNum(approvalWarehouseExtra.getExpressNo());
          }
          if (
              StrUtil.isNotBlank(approvalWarehouseExtra.getWarehouseCode())
              && !approvalWarehouseExtra.getWarehouseCode().equals(inForm.getWarehouseCode()))
          {
            inForm.setWarehouseCode(approvalWarehouseExtra.getWarehouseCode());
            inApplyForm.setWarehouseCode(approvalWarehouseExtra.getWarehouseCode());
              inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
                  supplierOrder.getGroupCode(), approvalWarehouseExtra.getWarehouseCode(),
                  Constants.STATE_OK).ifPresent(
                  inventoryLocation -> {
                    inForm.setWarehouseName(inventoryLocation.getWarehouseName());
                    inApplyForm.setWarehouseName(inventoryLocation.getWarehouseName());
                  }
              );
          }
          // 更新入库单 和 入库申请单信息
          supplierOrderToFormV2Repository.saveAndFlush(inForm);
          supplierOrderToFormV2Repository.saveAndFlush(inApplyForm);
          Map<String, ApprovalWarehouseExtraDetail> extraDetailMap =
              approvalWarehouseExtra.getDetails().stream().collect(Collectors.toMap(
                  ApprovalWarehouseExtraDetail::getSortNumber, item -> item,
                  (existing, replacement) -> existing
              ));
          BigDecimal totalPrice = BigDecimal.ZERO;
          BigDecimal totalNum = BigDecimal.ZERO;
          for (SupplierOrderDetailV2 inDetail : inDetails) {
            // 更新inDetail的信息
            inDetail.setWarehouse(inForm.getWarehouseCode());
            inDetail.setWarehouseName(inForm.getWarehouseName());
            BigDecimal originInQty = inDetail.getStockInputQty();
            ApprovalWarehouseExtraDetail approvalWarehouseExtraDetail = extraDetailMap.get(inDetail.getSortNum().toString());
            if (approvalWarehouseExtraDetail == null) {
              throw new CheckException("入库单明细不存在");
            }
            BigDecimal cancelQty = NumberUtil.sub(originInQty, approvalWarehouseExtraDetail.getInQty());
            BigDecimal stockInputQty = approvalWarehouseExtraDetail.getInQty();
            inDetail.setStockInputQty(stockInputQty);
            inDetail.setInvoicableNum(stockInputQty);
            totalNum = NumberUtil.add(totalNum, stockInputQty);
            totalPrice = totalPrice.add(inDetail.getPrice().multiply(stockInputQty));
            // todo 可能有特殊情况
            // 免费行和寄售的行数量设置为0
            if (inDetail.getFreeState().equals(SimpleBooleanEnum.YES.getKey())
                || inDetail.getProjectType().equals(Constants.PROJECT_TYPE_JS)) {
              inDetail.setInvoicableNum(BigDecimal.ZERO);
            }

            String detailedId = inDetail.getDetailedId();
            SupplierOrderDetailV2 detail = detailMap.get(detailedId);
            SupplierOrderDetailV2 inApplyDetail = inApplyDetailMap.get(detailedId);
            // @物料明细 待申请入库数量++
            detail.setWaitQty(NumberUtil.add(detail.getWaitQty(), cancelQty));
            // @物料明细 已申请入库数量--
            detail.setShipQty(NumberUtil.sub(detail.getShipQty(), cancelQty));
            // @物料明细 入库数量++
            detail.setStockInputQty(NumberUtil.add(detail.getStockInputQty(), stockInputQty));
            // @物料明细 实际交货++
            detail.setSettleQty(NumberUtil.add(detail.getSettleQty(), stockInputQty));
            // @入库申请单 已入库数量++
            inApplyDetail.setStockInputQty(NumberUtil.add(inApplyDetail.getStockInputQty(), stockInputQty));
            // @入库申请单 取消入库数量 ++
            inApplyDetail.setCancelQty(NumberUtil.add(inApplyDetail.getCancelQty(), cancelQty));
            inApplyDetail.setWarehouse(inForm.getWarehouseCode());
            inApplyDetail.setWarehouseName(inForm.getWarehouseName());
          }
          inForm.setNum(totalNum);
          inForm.setReturnPrice(totalPrice);
          supplierOrderDetailV2Repository.saveAll(inApplyDetails);
          supplierOrderDetailV2Repository.saveAll(details);
          supplierOrderDetailV2Repository.saveAll(inDetails);
          supplierOrderDetailV2Repository.flush();
          supplierOrderToFormV2Repository.saveAndFlush(inForm);
          // （入库数量合计-退库数量合计）/（订货数量合计-取消订货数量合计-退库数量合计）
          String progress = purchaseOrderV2BaseService.setStockProgress(supplierOrder);
          supplierOrder.setStockProgress(progress);
          if (NumberUtil.isGreaterOrEqual(
              supplierOrder.getTotalStockInputQty(), supplierOrder.getTotalNum())) {
            supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
          } else {
            supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getOrderState());
          }
          supplierOrderV2Repository.saveAndFlush(supplierOrder);
          this.sapSync(supplierOrder, inForm, inDetails);
        }
        return null;
      });
    }finally {
      lockUtils.unlockAllLocks(rLocks);
      lockUtils.unlockAllLocks(rLocks2);
    }
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.GO_DOWN_ENTRY_PAGE_V2)
  public PageResult<PurchaseOrderWarehousingV2DTO> warehouseWarrantPage(WarehouseEntryListParamV2 param, User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList()
        .contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList = permissionTypeProvider.getConcatNumUserNameList(user.getId(),
          Constants.USER_PERMISSION_SUPPLIER_ORDER,
          ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    PageResult<WarehousingV2DTO> pageResult =
        supplierOrderToFormDao.warehousingPageRef(param.toQueryMap(userNameList, purchaseId, createMan));
    List<WarehousingV2DTO> content = pageResult.getContent();
    List<String> detailIds =
        content.stream().map(WarehousingV2DTO::getDetailId).distinct().collect(Collectors.toList());
    List<InputInvoiceOrderWithDetailV2> inputInvoiceOrderWithDetailList =
        shareInputInvoiceProvider.getOrderInvoiceRelationListByDetailIdsRef(detailIds);
    List<PurchaseOrderWarehousingV2DTO> result = content.stream().map(warehousingDTO -> {
      PurchaseOrderWarehousingV2DTO purchaseOrderWarehousingDTO =
          new PurchaseOrderWarehousingV2DTO(warehousingDTO);
      // 根据detailId过滤出相应的inputInvoice
      List<InputInvoiceOrderWithDetailV2> filterInputInvoiceOrderWithDetail = inputInvoiceOrderWithDetailList.stream()
          .filter(item -> item.getDistinctDetailIds().contains(warehousingDTO.getDetailId()))
          .collect(Collectors.toList());
      List<PurchaseOrderInvoiceRelationV2> inputInvoiceOrders = filterInputInvoiceOrderWithDetail.stream()
          .map(item -> new PurchaseOrderInvoiceRelationV2(item.getInputInvoiceOrder())).distinct()
          .collect(Collectors.toList());
      purchaseOrderWarehousingDTO.setPurchaseOrderInvoiceRelationList(inputInvoiceOrders);
      return purchaseOrderWarehousingDTO;
    }).collect(Collectors.toList());
    return new PageResult<>(result, pageResult.getTotalCount(), pageResult.getTotalPages(),
        pageResult.getPageNo(), pageResult.getPageSize());

  }

  @Override
  @DefaultSearchScheme(searchType = Constants.GO_DOWN_ENTRY_PAGE_V2)
  public PurchaseOrderWarehousingStatistics warehouseStatistics(WarehouseEntryListParamV2 param, User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList()
        .contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList = permissionTypeProvider.getConcatNumUserNameList(user.getId(),
          Constants.USER_PERMISSION_SUPPLIER_ORDER,
          ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    return supplierOrderToFormDao.warehousingStatistics2(param.toQueryMap(userNameList, purchaseId, createMan));
  }

  @Override
  public void exportWarehouseWarrant(User user, WarehouseEntryListV2Params warehouseEntryListParams) {
    checkOrderExportPermission(user.getId());
    warehouseEntryListParams.setPageNo(1);
    warehouseEntryListParams.setPageSize(Integer.MAX_VALUE);
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList()
        .contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList =
          permissionTypeProvider.getConcatNumUserNameList(
              user.getId(),
              Constants.USER_PERMISSION_SUPPLIER_ORDER,
              ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    Map<String, Object> queryMap =
        warehouseEntryListParams.toQueryMap(userNameList, purchaseId, createMan);
    queryMap.put("version", ShardingContext.getVersion());
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        "导出-2.0入库单信息",
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        null,
        ""
    );
    //    batchTaskMqSender.toHandleBatchTask(mission.getId(),
    // JSON.toJSONString(exportWarehouseWarrantParams),
    //        Constants_Batch.BATCH_TASK_EXPORT_STORAGE_ORDER);
    missionRepository.saveAndFlush(mission);
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(queryMap),
        MissionTypeEnum.BATCH_TASK_EXPORT_STORAGE_ORDER_V2);
  }


  private void checkOrderExportPermission(String id) {
    String permissionCode = permissionTypeProvider.getUserPermissionCodeByUserIdAndType(id,
        Constants.USER_PERMISSION_EXPORT_WAREHOUSE_RETURN);
    permissionCode = StrUtil.blankToDefault(permissionCode,Constants.NOT_EXPORT_IMPORT_KEY);
    if (StrUtil.equals(permissionCode,Constants.NOT_EXPORT_IMPORT_KEY)) {
      throw new CheckException("您没有导出入库单/退库单的权限！");
    }
  }
}
