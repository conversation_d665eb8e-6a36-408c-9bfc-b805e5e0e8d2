package com.xhgj.srm.v2.factory;
/**
 * @since 2025/5/14 14:44
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.BootDictEnum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.supplierOrderForm.SupplierOrderFormCodeGenerator;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.sharding.algorithm.impl.SnowflakeString32Algorithm;
import com.xhgj.srm.jpa.util.SnowflakeIdGenerator;
import com.xhgj.srm.request.ConstantHZero;
import com.xhgj.srm.request.config.HZeroProcessConfig;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderWarehouseApplyForm;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseApplyV2AddForm;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseApplyV2AddForm.PurchaseOrderWarehouseApplyAddFormDetail;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseApplyV2AddFormSap;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddForm;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddForm.PurchaseOrderWarehouseAddFormDetail;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddFormSap;
import com.xhgj.srm.v2.form.feida.SupplierOrderWarehouseApplyProcessForm;
import com.xhgj.srm.v2.form.feida.SupplierOrderWarehouseApplyProcessForm.SupplierOrderWarehouseApplyProcessFormItem;
import com.xhgj.srm.v2.vo.purchaseOrder.form.WarehouseApplyFormDetailVO;
import com.xhgj.srm.v2.vo.purchaseOrder.form.WarehouseApplyFormDetailVO.WarehouseApplyDetailVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.dict.core.service.BootDictService;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/5/14 14:44:35
 *@description
 */
@Component
public class PurchaseOrderWarehouseApplyV2Factory {

  @Resource
  RedissonClient redissonClient;
  @Resource
  BootDictService bootDictService;
  @Resource
  HZeroProcessConfig hZeroProcessConfig;

  private static final String DEFAULT_STORE_TYPE = "2";

  private static final String DEFAULT_ORDER_TYPE = "SRM采购订单";

  private static final String DEFAULT_SOURCE = "2";

  private static final String DEFAULT_STATUS = "2";

  /**
   * #check 基础校验，判断是否为空 + 是否本次申请数量超过待申请入库数量
   */
  public void checkChooseProductDetails(PurchaseOrderWarehouseApplyV2AddForm form, List<SupplierOrderDetailV2> details) {
    if (CollUtil.isEmpty(details)) {
      throw new CheckException("没有找到对应的采购订单明细");
    }
    // 判断是否超过400行
    if (details.size() > 400) {
      throw new CheckException("申请入库明细不能超过400行");
    }
    Map<String, SupplierOrderDetailV2> detailMap = details.stream()
        .collect(Collectors.toMap(SupplierOrderDetailV2::getId, one -> one));
    List<PurchaseOrderWarehouseApplyAddFormDetail> productDetailList = form.getProductDetailList();
    for (PurchaseOrderWarehouseApplyAddFormDetail one : productDetailList) {
      String id = one.getId();
      SupplierOrderDetailV2 detail = detailMap.get(id);
      if (detail == null) {
        throw new CheckException("没有找到对应的采购订单明细");
      }
      // 申请数量
      BigDecimal applyNum = one.getApplyNum();
      // 待申请入库数量
      BigDecimal waitApplyNum = detail.getWaitQty();
      // 判断申请数量是否大于待申请入库数量
      if (applyNum.compareTo(waitApplyNum) > 0) {
        throw new CheckException("本次申请入库数量超过待申请入库数量");
      }
    }
  }

  /**
   * #check 基础校验直销库，快递公司和物流单号不能为空
   * @param form
   */
  public void checkLogisticsInfo(PurchaseOrderWarehouseApplyV2AddForm form) {
    if (WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(form.getWarehouseCode())) {
      if (StrUtil.isBlank(form.getLogisticsCompany())) {
        throw new CheckException("快递公司不能为空");
      }
      if (StrUtil.isBlank(form.getTrackNum())) {
        throw new CheckException("物流单号不能为空");
      }
    }
  }

  /**
   * #check 基础校验，判断是否行数量超过400条
   */
  public void checkChooseProductDetails(List<SupplierOrderDetailV2> details) {
    // 判断是否超过400行
    if (details.size() > 400) {
      throw new CheckException("提交的明细行，需要质检和不需要质检里，分别最多为400条");
    }
  }

  public SupplierOrderToFormV2 createWarehouseApplyNoQualityCheck(PurchaseOrderWarehouseApplyV2AddForm form,
      List<SupplierOrderDetailV2> noQualityCheckList, User user) {
    SupplierOrderToFormV2 warehouseApplyBase = this.createWarehouseApplyBase(form,
        noQualityCheckList, user);
    if (warehouseApplyBase == null) {
      return null;
    }
    warehouseApplyBase.setStatus(SupplierOrderFormStatus.WAIT_RECEIPT.getStatus());
    warehouseApplyBase.setQualityCheck(false);
    warehouseApplyBase.setCallStatus(SupplierOrderFormCallStatus.NO_CALL.getStatus());
    if (form.getSource().equals("SAP")) {
      warehouseApplyBase.setQualityCheck(null);
      warehouseApplyBase.setWarehousing(true);
    }
    return warehouseApplyBase;
  }

  public SupplierOrderToFormV2 createWarehouseApplyQualityCheck(PurchaseOrderWarehouseApplyV2AddForm form, List<SupplierOrderDetailV2> qualityCheckList, User user) {
    SupplierOrderToFormV2 warehouseApplyBase = this.createWarehouseApplyBase(form,
        qualityCheckList, user);
    if (warehouseApplyBase == null) {
      return null;
    }
    warehouseApplyBase.setStatus(SupplierOrderFormStatus.WAIT_RECEIPT.getStatus());
    warehouseApplyBase.setQualityCheck(true);
    return warehouseApplyBase;
  }

  private SupplierOrderToFormV2 createWarehouseApplyBase(PurchaseOrderWarehouseApplyV2AddForm form, List<SupplierOrderDetailV2> details, User user) {
    if (CollUtil.isEmpty(details)) {
      return null;
    }
    SupplierOrderToFormV2 supplierOrderToForm = new SupplierOrderToFormV2();
    List<PurchaseOrderWarehouseApplyAddFormDetail> productDetailList = form.getProductDetailList();
    Map<String, PurchaseOrderWarehouseApplyAddFormDetail> addMap = productDetailList.stream()
        .collect(Collectors.toMap(PurchaseOrderWarehouseApplyAddFormDetail::getId, one -> one));
    // 计算结算价格
    BigDecimal totalPrice = BigDecimal.ZERO;
    BigDecimal totalNum = BigDecimal.ZERO;
    for (SupplierOrderDetailV2 detail : details) {
      PurchaseOrderWarehouseApplyAddFormDetail addFormDetail = addMap.get(detail.getId());
      if (addFormDetail == null) {
        continue;
      }
      BigDecimal applyNum = addFormDetail.getApplyNum();
      BigDecimal price = detail.getPrice();
      totalPrice = totalPrice.add(price.multiply(applyNum));
      totalNum = totalNum.add(applyNum);
    }
    supplierOrderToForm.setReturnPrice(totalPrice);
    supplierOrderToForm.setSource(form.getSource());
    supplierOrderToForm.setWarehouseName(form.getWarehouseName());
    supplierOrderToForm.setWarehouseCode(form.getWarehouseCode());
    supplierOrderToForm.setPostingDate(form.getPostingDate());
    supplierOrderToForm.setFormCode(SupplierOrderFormCodeGenerator.INSTANCE.generate(redissonClient,
        SupplierOrderFormType.DELIVER));
    if (user != null) {
      supplierOrderToForm.setCreateUser(user.getId());
      supplierOrderToForm.setCreateUserName(user.getRealName());
    }
    supplierOrderToForm.setSupplierOrderId(form.getPurchaserOrderId());
    supplierOrderToForm.setType(SupplierOrderFormType.DELIVER.getType());
    supplierOrderToForm.setCreateTime(System.currentTimeMillis());
    supplierOrderToForm.setTime(System.currentTimeMillis());
    supplierOrderToForm.setLogisticsCompany(form.getLogisticsCompany());
    supplierOrderToForm.setLogisticsCode(form.getLogisticsCode());
    supplierOrderToForm.setTrackNum(form.getTrackNum());
    supplierOrderToForm.setCode(form.getCode());
    supplierOrderToForm.setState(Constants.STATE_OK);
    supplierOrderToForm.setUpdateTime(System.currentTimeMillis());
    supplierOrderToForm.setNum(totalNum);
    supplierOrderToForm.setWarehousing(false);
    return supplierOrderToForm;
  }

  public List<SupplierOrderDetailV2> createWarehouseApplyDetails(PurchaseOrderWarehouseApplyV2AddForm form, SupplierOrderToFormV2 supplierOrderToForm, List<SupplierOrderDetailV2> details) {
    List<SupplierOrderDetailV2> warehouseApplyDetails = new ArrayList<>();
    if (CollUtil.isEmpty(details)) {
      return warehouseApplyDetails;
    }
    Map<String, PurchaseOrderWarehouseApplyAddFormDetail> addMap = form.getProductDetailList().stream()
        .collect(Collectors.toMap(PurchaseOrderWarehouseApplyAddFormDetail::getId, one -> one));
    int index = 1;
    for (SupplierOrderDetailV2 detail : details) {
      SupplierOrderDetailV2 warehouseApplyDetail = MapStructFactory.INSTANCE.toSupplierOrderDetailV2(detail);
      warehouseApplyDetail.setIndex(String.valueOf(index));
      warehouseApplyDetail.setOrderToFormType(supplierOrderToForm.getType());
      warehouseApplyDetail.setId(null);
      warehouseApplyDetail.setDetailed(detail);
      warehouseApplyDetail.setDetailedId(detail.getId());
      warehouseApplyDetail.setOrderToFormId(supplierOrderToForm.getId());
      PurchaseOrderWarehouseApplyAddFormDetail addForm = addMap.get(detail.getId());
      warehouseApplyDetail.setNum(addForm.getApplyNum());
      warehouseApplyDetail.setCreateTime(System.currentTimeMillis());
      warehouseApplyDetail.setUpdateTime(System.currentTimeMillis());
      warehouseApplyDetail.setWarehouse(supplierOrderToForm.getWarehouseCode());
      warehouseApplyDetail.setWarehouseName(supplierOrderToForm.getWarehouseName());
      warehouseApplyDetail.setTotalAmountIncludingTax(warehouseApplyDetail.getPrice().multiply(warehouseApplyDetail.getNum()));
      warehouseApplyDetail.setTotalPrice(warehouseApplyDetail.getTotalAmountIncludingTax());
      warehouseApplyDetail.setEntrustDetailId(null);
      warehouseApplyDetail.setSapRowId(null);
      if (Boolean.TRUE.equals(warehouseApplyDetail.getSupplierOrderProduct().getQualityCheck())) {
        warehouseApplyDetail.setInspectQty(BigDecimal.ZERO);
      } else {
        warehouseApplyDetail.setInspectQty(null);
      }
      warehouseApplyDetail.setStockInputQty(BigDecimal.ZERO);
      warehouseApplyDetail.setCancelQty(BigDecimal.ZERO);
      warehouseApplyDetail.setPurchaseOrderId(form.getPurchaserOrderId());
      warehouseApplyDetails.add(warehouseApplyDetail);
      //@物料明细 待申请入库数量 --
      detail.setWaitQty(detail.getWaitQty().subtract(warehouseApplyDetail.getNum()));
      //@物料明细 已申请入库数量 ++
      detail.setShipQty(detail.getShipQty().add(warehouseApplyDetail.getNum()));
      index++;
    }
    return warehouseApplyDetails;
  }

  public PurchaseOrderWarehouseV2AddForm createWarehouseAddForm(PurchaseOrderWarehouseApplyV2AddForm form, SupplierOrderToFormV2 inApplyForm, List<SupplierOrderDetailV2> inApplyDetails) {
    PurchaseOrderWarehouseV2AddForm addForm = new PurchaseOrderWarehouseV2AddForm();
    if (form instanceof PurchaseOrderWarehouseApplyV2AddFormSap) {
      addForm = new PurchaseOrderWarehouseV2AddFormSap();
      PurchaseOrderWarehouseApplyV2AddFormSap addFormSap = (PurchaseOrderWarehouseApplyV2AddFormSap) form;
      ((PurchaseOrderWarehouseV2AddFormSap) addForm).setProductVoucher(addFormSap.getProductVoucher());
      ((PurchaseOrderWarehouseV2AddFormSap) addForm).setProductVoucherYear(addFormSap.getProductVoucherYear());
      ((PurchaseOrderWarehouseV2AddFormSap) addForm).setProductInfoList(addFormSap.getProductInfoList());
    }
    addForm.setPurchaseOrderId(form.getPurchaserOrderId());
    addForm.setWarehouseApplyId(inApplyForm.getId());
    List<PurchaseOrderWarehouseAddFormDetail> productDetailList = new ArrayList<>();
    for (SupplierOrderDetailV2 detail : inApplyDetails) {
      PurchaseOrderWarehouseAddFormDetail addFormDetail = new PurchaseOrderWarehouseAddFormDetail();
      addFormDetail.setId(detail.getId());
      addFormDetail.setApplyNum(detail.getNum());
      productDetailList.add(addFormDetail);
    }
    addForm.setProductDetailList(productDetailList);
    return addForm;
  }

  public List<WarehouseApplyFormDetailVO> buildDetailVOS(List<SupplierOrderToFormV2> warehouseApplyList, Map<String, List<SupplierOrderDetailV2>> warehouseApplyDetailsMap) {
    return warehouseApplyList.stream().map(warehouseApply -> {
      List<SupplierOrderDetailV2> warehouseApplyDetails =
          warehouseApplyDetailsMap.getOrDefault(warehouseApply.getId(), new ArrayList<>());
      return this.buildDetailVO(warehouseApply, warehouseApplyDetails);
    }).collect(Collectors.toList());
  }

  public WarehouseApplyFormDetailVO buildDetailVO(SupplierOrderToFormV2 warehouseApply,
      List<SupplierOrderDetailV2> warehouseApplyDetails) {
    WarehouseApplyFormDetailVO vo = new WarehouseApplyFormDetailVO();
    vo.setId(warehouseApply.getId());
    vo.setFormCode(warehouseApply.getFormCode());
    vo.setSource(warehouseApply.getSource());
    vo.setQualityCheck(warehouseApply.getQualityCheck());
    vo.setCreateTime(warehouseApply.getCreateTime());
    vo.setPostingDate(warehouseApply.getPostingDate());
    vo.setLogisticsCompany(warehouseApply.getLogisticsCompany());
    vo.setLogisticsCode(warehouseApply.getLogisticsCode());
    vo.setTrackNum(warehouseApply.getTrackNum());
    vo.setStatus(warehouseApply.getStatus());
    vo.setReviewStatus(warehouseApply.getReviewStatus());
    vo.setWarehouseCode(warehouseApply.getWarehouseCode());
    vo.setWarehouseName(warehouseApply.getWarehouseName());
    vo.setCallStatus(warehouseApply.getCallStatus());
    vo.setCallTime(warehouseApply.getCallTime());
    List<WarehouseApplyDetailVO> details = new ArrayList<>();
    for (SupplierOrderDetailV2 warehouseApplyDetail : warehouseApplyDetails) {
      WarehouseApplyDetailVO detailVO = new WarehouseApplyDetailVO();
      detailVO.setId(warehouseApplyDetail.getId());
      detailVO.setIndex(warehouseApplyDetail.getIndex());
      SupplierOrderProductV2 supplierOrderProduct = warehouseApplyDetail.getSupplierOrderProduct();
      detailVO.setProductCode(supplierOrderProduct.getCode());
      detailVO.setBrand(supplierOrderProduct.getBrand());
      detailVO.setProductName(supplierOrderProduct.getName());
      detailVO.setSpecification(supplierOrderProduct.getSpecification());
      detailVO.setModel(supplierOrderProduct.getModel());
      BigDecimal num = BigDecimalUtil.setScaleBigDecimalHalfUp(warehouseApplyDetail.getNum(), 3);
      detailVO.setNum(num);
      detailVO.setWarehouseName(warehouseApplyDetail.getWarehouseName());
      detailVO.setWarehouseCode(warehouseApplyDetail.getWarehouse());
      detailVO.setQualityCheck(supplierOrderProduct.getQualityCheck());
      Optional.ofNullable(warehouseApplyDetail.getInspectQty()).ifPresent(
          item -> detailVO.setInspectQty(BigDecimalUtil.setScaleBigDecimalHalfUp(warehouseApplyDetail.getInspectQty(), 3))
      );
      BigDecimal stockOutQty = BigDecimalUtil.setScaleBigDecimalHalfUp(warehouseApplyDetail.getStockInputQty(), 3);
      detailVO.setStockInputQty(stockOutQty);
      BigDecimal cancelQty = BigDecimalUtil.setScaleBigDecimalHalfUp(warehouseApplyDetail.getCancelQty(), 3);
      detailVO.setCancelQty(cancelQty);
      detailVO.setUnit(supplierOrderProduct.getUnit());
      detailVO.setPrice(warehouseApplyDetail.getPrice());
      detailVO.setUnitCode(supplierOrderProduct.getUnitCode());
      detailVO.setSortNum(warehouseApplyDetail.getSortNum());
      details.add(detailVO);
    }
    vo.setDetails(details);
    return vo;
  }

  /**
   * 构建飞搭表单数据
   * @param supplierOrder
   * @param inApplyForm
   * @param inApplyDetails
   */
  public List<SupplierOrderWarehouseApplyForm>  createFeidaData(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inApplyForm,
      List<SupplierOrderDetailV2> inApplyDetails) {
    List<SupplierOrderWarehouseApplyForm> forms = new ArrayList<>();
    for (SupplierOrderDetailV2 inApplyDetail : inApplyDetails) {
      SupplierOrderProductV2 supplierOrderProduct = inApplyDetail.getSupplierOrderProduct();
      SupplierOrderWarehouseApplyForm form = new SupplierOrderWarehouseApplyForm();
      form.setOrderType(DEFAULT_ORDER_TYPE);
      form.setFormCodeAndIndex(inApplyForm.getFormCode() + "-" + inApplyDetail.getIndex());
      form.setProductCode(supplierOrderProduct.getCode());
      form.setProductName(supplierOrderProduct.getName());
      form.setGroupCode(supplierOrder.getGroupCode());
      form.setGroupName(supplierOrder.getGroupName());
      form.setWarehouseCode(inApplyDetail.getWarehouse());
      form.setBatchNo(inApplyDetail.getBatchNo());
      form.setSpecification(supplierOrderProduct.getSpecification());
      form.setModel(supplierOrderProduct.getModel());
      form.setUnitCode(supplierOrderProduct.getUnitCode());
      form.setNum(inApplyDetail.getNum().stripTrailingZeros().toPlainString());
      form.setApplyNum(inApplyDetail.getNum().stripTrailingZeros().toPlainString());
      form.setAlreadyInStockNum(BigDecimal.ZERO.stripTrailingZeros().toPlainString());
      form.setStatus(DEFAULT_STATUS);
      form.setIsQualityInspection(SimpleBooleanEnum.YES.getValue());
      form.setSource(DEFAULT_SOURCE);
      form.setStoreType(DEFAULT_STORE_TYPE);
      forms.add(form);
    }
    return forms;
  }

  public StartProcessParam createFeidaDataProcess(SupplierOrderV2 supplierOrder,
      SupplierOrderToFormV2 inApplyForm, List<SupplierOrderDetailV2> inApplyDetails, User user) {
    List<SupplierOrderWarehouseApplyForm> forms = this.createFeidaData(supplierOrder, inApplyForm, inApplyDetails);
    String desc = StrUtil.format("{}提交的采购入库申请单{}-{}", user.getRealName(),
        inApplyForm.getFormCode(),
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    // 生成唯一id
    SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator();
    Comparable<?> comparable = snowflakeIdGenerator.getSnowflakeGenerator().generateKeyOrigin();
    SupplierOrderWarehouseApplyProcessForm processForm = new SupplierOrderWarehouseApplyProcessForm();
    Long id = Convert.toLong(comparable);
    processForm.setId(id);
    List<SupplierOrderWarehouseApplyProcessFormItem> items = new ArrayList<>();
    for (SupplierOrderWarehouseApplyForm supplierOrderWarehouseApplyForm : forms) {
      SupplierOrderWarehouseApplyProcessFormItem item =
          MapStructFactory.INSTANCE.toSupplierOrderWarehouseApplyProcessFormItem(
              supplierOrderWarehouseApplyForm);
      item.setTitleid(id);
      items.add(item);
    }
    processForm.setItems(items);
    StartProcessParam processParam = StartProcessParam.builder().flowKey(hZeroProcessConfig.getWarehouseApplyFlowKey())
        .businessKey(desc).dimension(ConstantHZero.DIMENSION_ORG)
        .starter(user.getCode().toLowerCase()).description(desc).variableMap(null)
        .docJsonMap(JSON.parseObject(JSON.toJSONString(processForm))).build();
    return processParam;
  }
}
