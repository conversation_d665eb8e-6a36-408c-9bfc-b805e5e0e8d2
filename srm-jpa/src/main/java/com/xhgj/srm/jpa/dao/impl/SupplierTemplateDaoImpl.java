package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.SupplierTemplateDao;
import com.xhgj.srm.jpa.entity.SupplierTemplate;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2022/7/5 16:08
 */
@Repository
public class SupplierTemplateDaoImpl extends AbstractExtDao<SupplierTemplate> implements SupplierTemplateDao {

  @Override
  public Page<SupplierTemplate> findPage(String companyName, int pageNo, int pageSize) {
    String hql = "from SupplierTemplate sig where sig.state = ? and sig.group.state = ? ";
    Object[] params = new Object[]{Constants.STATE_OK,Constants.STATE_OK};
    if (StrUtil.isNotEmpty(companyName)) {
      hql+="and sig.group.name like ? ";
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(companyName, "%"));
    }
    hql+="order by sig.createTime ";
    return findPage(hql,params,pageNo,pageSize);
  }

  @Override
  public SupplierTemplate getById(String id) {
    Assert.notEmpty(id);
    String hql = "from SupplierTemplate where state = ? and id = ? ";
    Object[] params = new Object[]{Constants.STATE_OK,id};
    return getFirstHqlEntity(hql,params);
  }

  @Override
  public SupplierTemplate getSupplierTemplateByGroupId(String groupId) {
    Assert.notEmpty(groupId);
    String hql = "from SupplierTemplate where state = ? and groupId = ? ";
    Object[] params = new Object[]{Constants.STATE_OK,groupId};
    return getFirstHqlEntity(hql,params);
  }
}
