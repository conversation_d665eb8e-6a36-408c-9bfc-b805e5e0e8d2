package com.xhgj.srm.v2.form.feida;/**
*<AUTHOR>
*@since  2025/6/9 17:45
*/

import com.alibaba.fastjson.annotation.JSONField;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderWarehouseApplyForm;
import lombok.Data;
import java.util.List;

/**
*<AUTHOR>
*@date 2025/6/9 17:45:44
*@description
*/
@Data
public class SupplierOrderWarehouseApplyProcessForm {

  /**
   * 唯一id
   */
  private Long id;

  /**
   * items
   */
  @JSONField(name = "XHGJ_PP01_APPLY")
  private List<SupplierOrderWarehouseApplyProcessFormItem> items;

  @Data
  public static class SupplierOrderWarehouseApplyProcessFormItem extends SupplierOrderWarehouseApplyForm {
    private Long titleid;
  }

}
